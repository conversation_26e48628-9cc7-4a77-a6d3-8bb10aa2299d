import React from 'react';
import { <PERSON>, Text<PERSON>ield, Button, Typography, styled } from '@mui/material';

// Styled component for the background with the gradient and placeholder for wavy pattern
const BackgroundBox = styled(Box)({
  minHeight: '100vh',
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  // Refined gradient colors to closely match the image
  background: 'linear-gradient(135deg, #e53935 0%, #ff5722 50%, #ff9800 100%)',
  position: 'relative',
  overflow: 'hidden',
  // For the exact wavy pattern, a background-image (SVG) would be ideal.
  // Example: backgroundImage: `url('/path/to/wavy-pattern.svg')`,
  // backgroundSize: 'cover',
  // backgroundRepeat: 'no-repeat',
});

// Styled component for the main content box (the translucent card)
const ContentBox = styled(Box)({
  backgroundColor: 'rgba(255, 255, 255, 0.15)', // Slightly more opaque white
  backdropFilter: 'blur(8px)', // Adjusted blur intensity
  padding: '40px 50px', // Adjusted padding for wider appearance
  borderRadius: '15px',
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  gap: '25px', // Increased gap between elements
  boxShadow: '0 8px 32px 0 rgba(0, 0, 0, 0.2)', // Darker, more pronounced shadow
  width: 'calc(100% - 80px)', // Adjust width to be responsive but not too wide
  maxWidth: '400px', // Max width for larger screens
  margin: '0 20px', // Add some horizontal margin
});

// Styled component for the logo (triangle shape) with an inner placeholder for the exact logo SVG
const LogoContainer = styled(Box)({
  width: 0,
  height: 0,
  borderLeft: '55px solid transparent', // Slightly larger triangle
  borderRight: '55px solid transparent',
  borderBottom: '100px solid white',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  position: 'relative',
  marginBottom: '10px',
});

const InnerLogoPlaceholder = styled(Box)({
  position: 'absolute',
  top: '40px', // Adjust to visually center the logo within the triangle
  // For the exact logo, replace this with an Image or an SVG component
  // <img src="/path/to/your/logo.svg" alt="Liberal Bites Logo" style={{ width: '50px', height: '50px' }} />
  width: '50px',
  height: '50px',
  backgroundColor: 'transparent', // transparent, awaiting SVG
  borderRadius: '50%',
  border: '2px solid #ff5722', // Example border for placeholder
});

const BookIllustration = styled(Box)({
  position: 'absolute',
  bottom: '40px', // Moved slightly up
  left: '40px', // Moved slightly right
  width: '120px', // Adjusted size
  height: '120px', // Adjusted size
  // This should be replaced with the actual book illustration SVG.
  // Example: <img src="/path/to/book-illustration.svg" alt="Books" style={{ width: '100%', height: '100%' }} />
  backgroundColor: 'transparent',
  opacity: 0.3, // More subtle opacity
  // Placeholder for visual representation
  border: '2px solid rgba(255, 255, 255, 0.7)',
  borderRadius: '10px',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  color: 'rgba(255, 255, 255, 0.7)',
  fontSize: '14px',
  textAlign: 'center',
  lineHeight: '1.2',
});

function Login() {
  return (
    <BackgroundBox>
      <ContentBox>
        <LogoContainer>
          {/* Replace InnerLogoPlaceholder with your actual SVG logo component or image tag */}
          <InnerLogoPlaceholder />
        </LogoContainer>
        <Typography variant="h4" sx={{ color: 'white', fontWeight: 'bold', marginBottom: '15px', fontSize: '2.2rem' }}>
          LIBERAL BITES
        </Typography>
        <TextField
          label="Email"
          variant="filled"
          fullWidth
          sx={{
            backgroundColor: 'white',
            borderRadius: '8px', // More rounded
            '& .MuiFilledInput-root': {
              backgroundColor: 'white',
              borderRadius: '8px',
              padding: '12px 15px', // Adjusted padding
            },
            '& .MuiInputLabel-root': {
                color: 'rgba(0, 0, 0, 0.6)',
                fontWeight: 'bold', // Make label bold
                transform: 'translate(15px, 12px) scale(1)', // Adjust label initial position
                '&.Mui-focused': {
                    color: '#ff5722', // Focus color
                },
            },
            '& .MuiInputBase-input': {
                color: 'rgba(0, 0, 0, 0.87)',
                padding: '12px 15px', // Ensure input text has consistent padding
            },
          }}
          InputProps={{ disableUnderline: true }} // Remove underline for filled variant
        />
        <TextField
          label="Password"
          type="password"
          variant="filled"
          fullWidth
          sx={{
            backgroundColor: 'white',
            borderRadius: '8px',
            '& .MuiFilledInput-root': {
              backgroundColor: 'white',
              borderRadius: '8px',
              padding: '12px 15px',
            },
            '& .MuiInputLabel-root': {
                color: 'rgba(0, 0, 0, 0.6)',
                fontWeight: 'bold',
                transform: 'translate(15px, 12px) scale(1)',
                '&.Mui-focused': {
                    color: '#ff5722',
                },
            },
            '& .MuiInputBase-input': {
                color: 'rgba(0, 0, 0, 0.87)',
                padding: '12px 15px',
            },
          }}
          InputProps={{ disableUnderline: true }}
        />
        <Button
          variant="contained"
          fullWidth // Button should be full width of the content box
          sx={{
            backgroundColor: '#EF5350',
            '&:hover': {
              backgroundColor: '#D32F2F',
            },
            padding: '15px 40px', // Increased vertical padding
            marginTop: '25px', // Increased margin top
            borderRadius: '8px',
            fontWeight: 'bold',
            fontSize: '1.1rem', // Slightly larger font
            textTransform: 'none', // Prevent uppercase transform
          }}
        >
          Login
        </Button>
      </ContentBox>
      {/* This should be an SVG for exact replication */}
      <BookIllustration>Book<br/>Illustration<br/>Placeholder</BookIllustration>
    </BackgroundBox>
  );
}

export default Login;
