import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import logo from '../assets/logo.svg';

const styles = {
  container: {
    minHeight: '100vh',
    background: '#F5E8C7', // AdminDashboard cream background
    fontFamily: 'Inter, sans-serif'
  },
  loginContainer: {
    minHeight: '100vh',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    overflow: 'hidden'
  },
  backgroundContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 0
  },
  backgroundImage: {
    width: '100%',
    height: '100%',
    objectFit: 'cover',
    opacity: 0.2
  },
  backgroundOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: 'linear-gradient(135deg, rgba(0, 109, 119, 0.1) 0%, rgba(255, 111, 97, 0.05) 50%, rgba(131, 197, 190, 0.1) 100%)' // AdminDashboard colors
  },
  loginCard: {
    position: 'relative',
    zIndex: 10,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    backdropFilter: 'blur(10px)',
    borderRadius: '24px',
    boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
    padding: '48px',
    width: '100%',
    maxWidth: '448px',
    margin: '32px 16px',
    border: '1px solid rgba(255, 255, 255, 0.2)'
  },
  logoSection: {
    textAlign: 'center',
    marginBottom: '40px'
  },
  logoContainer: {
    display: 'inline-flex',
    alignItems: 'center',
    justifyContent: 'center',
    width: '80px',
    height: '80px',
    borderRadius: '16px',
    marginBottom: '24px',
    boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)'
  },
  logoImage: {
    width: '80px',
    height: '80px',
    objectFit: 'contain'
  },
  logoIcon: {
    color: 'white',
    fontSize: '24px'
  },
  title: {
    fontSize: '30px',
    fontWeight: 'bold',
    color: '#006D77', // AdminDashboard teal color
    marginBottom: '8px'
  },
  subtitle: {
    color: '#4b5563',
    marginTop: '8px',
    fontWeight: '500'
  },
  form: {
    display: 'flex',
    flexDirection: 'column',
    gap: '24px'
  },
  fieldContainer: {
    display: 'flex',
    flexDirection: 'column',
    gap: '8px'
  },
  label: {
    display: 'block',
    fontSize: '14px',
    fontWeight: '600',
    color: '#006D77' // AdminDashboard teal color
  },
  inputContainer: {
    position: 'relative',
    transition: 'transform 0.2s ease'
  },
  inputContainerHover: {
    transform: 'scale(1.02)'
  },
  input: {
    width: '100%',
    padding: '16px',
    backgroundColor: '#f9fafb',
    border: '1px solid rgba(0, 109, 119, 0.3)', // AdminDashboard teal border
    borderRadius: '12px',
    outline: 'none',
    transition: 'all 0.2s ease',
    color: '#111827',
    fontSize: '16px',
    boxSizing: 'border-box'
  },
  inputFocus: {
    boxShadow: '0 0 0 3px rgba(255, 111, 97, 0.2)', // AdminDashboard coral focus
    borderColor: '#FF6F61' // AdminDashboard coral color
  },
  inputIcon: {
    position: 'absolute',
    top: '50%',
    right: '16px',
    transform: 'translateY(-50%)',
    color: 'rgba(0, 109, 119, 0.6)', // AdminDashboard teal icon
    fontSize: '14px'
  },
  registerButtonContainer: {
    paddingTop: '8px'
  },
  registerButton: {
    width: '100%',
    backgroundColor: '#FF6F61', // AdminDashboard coral color
    color: 'white',
    fontWeight: 'bold',
    padding: '16px 24px',
    borderRadius: '12px',
    border: 'none',
    cursor: 'pointer',
    boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
    transition: 'all 0.2s ease',
    fontSize: '16px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    gap: '8px'
  },
  registerButtonHover: {
    backgroundColor: 'rgba(255, 111, 97, 0.9)', // AdminDashboard coral hover
    boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1)',
    transform: 'scale(1.02)'
  },
  registerButtonDisabled: {
    opacity: 0.5,
    cursor: 'not-allowed'
  },
  messageContainer: {
    textAlign: 'center',
    paddingTop: '8px'
  },
  successMessage: {
    color: '#059669',
    fontWeight: '500',
    fontSize: '14px'
  },
  errorMessage: {
    color: '#dc2626',
    fontWeight: '500',
    fontSize: '14px'
  },
  loginSection: {
    textAlign: 'center',
    marginTop: '24px',
    paddingTop: '24px',
    borderTop: '1px solid #f3f4f6'
  },
  loginText: {
    color: '#4b5563'
  },
  loginLink: {
    color: '#FF6F61', // AdminDashboard coral color
    fontWeight: '600',
    cursor: 'pointer',
    transition: 'color 0.2s ease',
    marginLeft: '4px'
  },
  loginLinkHover: {
    color: '#006D77' // AdminDashboard teal color
  }
};

function Register() {
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    confirmPassword: ''
  });
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState('');

  // Check if user is already logged in
  useEffect(() => {
    const authToken = localStorage.getItem('authToken');
    if (authToken) {
      // User is already logged in, redirect to main menu
      navigate('/main-menu');
    }
  }, [navigate]);

  const [focusedFields, setFocusedFields] = useState({});
  const [hoveredFields, setHoveredFields] = useState({});
  const [buttonHovered, setButtonHovered] = useState(false);
  const [loginHovered, setLoginHovered] = useState(false);

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleFocus = (field) => {
    setFocusedFields(prev => ({
      ...prev,
      [field]: true
    }));
  };

  const handleBlur = (field) => {
    setFocusedFields(prev => ({
      ...prev,
      [field]: false
    }));
  };

  const handleHover = (field, isHovered) => {
    setHoveredFields(prev => ({
      ...prev,
      [field]: isHovered
    }));
  };

  const handleRegister = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    setMessage('');

    // Basic validation
    if (formData.password !== formData.confirmPassword) {
      setMessage('Passwords do not match');
      setIsLoading(false);
      return;
    }

    if (formData.password.length < 6) {
      setMessage('Password must be at least 6 characters long');
      setIsLoading(false);
      return;
    }

    try {
      console.log('Attempting registration with:', { 
        name: formData.name, 
        email: formData.email, 
        password: '***' 
      });

      const response = await fetch('http://168.231.122.170/cstm-api/api/admin/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: formData.name,
          email: formData.email,
          password: formData.password,
          password_confirmation: formData.confirmPassword,
          role: "user"
        })
      });

      console.log('Response status:', response.status);
      console.log('Response headers:', response.headers);

      if (response.ok) {
        const data = await response.json();
        console.log('Response data:', data);
        setMessage('Registration successful! Redirecting to login...');

        setTimeout(() => {
          navigate('/login');
        }, 2000);
      } else {
        // Try to parse error response
        try {
          const errorData = await response.json();
          console.log('Error response data:', errorData);
          setMessage(errorData.message || errorData.error || `Registration failed (${response.status})`);
        } catch (parseError) {
          console.log('Could not parse error response:', parseError);
          setMessage(`Registration failed (${response.status}): ${response.statusText}`);
        }
      }
    } catch (error) {
      console.error('Registration error:', error);

      // Provide more specific error messages
      if (error.name === 'TypeError' && error.message.includes('fetch')) {
        setMessage('Network error: Unable to connect to server. Please check your internet connection.');
      } else if (error.message.includes('CORS')) {
        setMessage('CORS error: Server configuration issue. Please contact support.');
      } else {
        setMessage(`Unable to register: ${error.message}`);
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div style={styles.container}>
      <div style={styles.loginContainer}>
        {/* Background Image and Overlay */}
        <div style={styles.backgroundContainer}>
          <img 
            style={styles.backgroundImage}
            src="https://storage.googleapis.com/uxpilot-auth.appspot.com/8e688dbc78-e30af8ef0257480e8948.png" 
            alt="cozy dining table with warm lighting, books and coffee, soft blur background"
          />
          <div style={styles.backgroundOverlay}></div>
        </div>
        
        {/* Registration Card */}
        <div style={styles.loginCard}>
          {/* Logo Section */}
          <div style={styles.logoSection}>
            <div style={styles.logoContainer}>
              <img 
                src={logo} 
                alt="Liberate Bites Logo"
                style={styles.logoImage}
              />
            </div>
            <h1 style={styles.title}>
              Liberate Bites
            </h1>
            <p style={styles.subtitle}>Create your account!</p>
          </div>

          {/* Registration Form */}
          <form onSubmit={handleRegister} style={styles.form}>
            {/* Name Field */}
            <div style={styles.fieldContainer}>
              <label htmlFor="name" style={styles.label}>
                Full Name
              </label>
              <div
                style={{
                  ...styles.inputContainer,
                  ...(hoveredFields.name ? styles.inputContainerHover : {})
                }}
                onMouseEnter={() => handleHover('name', true)}
                onMouseLeave={() => handleHover('name', false)}
              >
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  onFocus={() => handleFocus('name')}
                  onBlur={() => handleBlur('name')}
                  style={{
                    ...styles.input,
                    ...(focusedFields.name ? styles.inputFocus : {})
                  }}
                  placeholder="Enter your full name"
                  required
                />
                <div style={styles.inputIcon}>
                  <i className="fas fa-user"></i>
                </div>
              </div>
            </div>

            {/* Email Field */}
            <div style={styles.fieldContainer}>
              <label htmlFor="email" style={styles.label}>
                Email Address
              </label>
              <div
                style={{
                  ...styles.inputContainer,
                  ...(hoveredFields.email ? styles.inputContainerHover : {})
                }}
                onMouseEnter={() => handleHover('email', true)}
                onMouseLeave={() => handleHover('email', false)}
              >
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  onFocus={() => handleFocus('email')}
                  onBlur={() => handleBlur('email')}
                  style={{
                    ...styles.input,
                    ...(focusedFields.email ? styles.inputFocus : {})
                  }}
                  placeholder="Enter your email"
                  required
                />
                <div style={styles.inputIcon}>
                  <i className="fas fa-envelope"></i>
                </div>
              </div>
            </div>

            {/* Password Field */}
            <div style={styles.fieldContainer}>
              <label htmlFor="password" style={styles.label}>
                Password
              </label>
              <div
                style={{
                  ...styles.inputContainer,
                  ...(hoveredFields.password ? styles.inputContainerHover : {})
                }}
                onMouseEnter={() => handleHover('password', true)}
                onMouseLeave={() => handleHover('password', false)}
              >
                <input
                  type="password"
                  id="password"
                  name="password"
                  value={formData.password}
                  onChange={(e) => handleInputChange('password', e.target.value)}
                  onFocus={() => handleFocus('password')}
                  onBlur={() => handleBlur('password')}
                  style={{
                    ...styles.input,
                    ...(focusedFields.password ? styles.inputFocus : {})
                  }}
                  placeholder="Enter your password"
                  required
                />
                <div style={styles.inputIcon}>
                  <i className="fas fa-lock"></i>
                </div>
              </div>
            </div>

            {/* Confirm Password Field */}
            <div style={styles.fieldContainer}>
              <label htmlFor="confirmPassword" style={styles.label}>
                Confirm Password
              </label>
              <div
                style={{
                  ...styles.inputContainer,
                  ...(hoveredFields.confirmPassword ? styles.inputContainerHover : {})
                }}
                onMouseEnter={() => handleHover('confirmPassword', true)}
                onMouseLeave={() => handleHover('confirmPassword', false)}
              >
                <input
                  type="password"
                  id="confirmPassword"
                  name="confirmPassword"
                  value={formData.confirmPassword}
                  onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                  onFocus={() => handleFocus('confirmPassword')}
                  onBlur={() => handleBlur('confirmPassword')}
                  style={{
                    ...styles.input,
                    ...(focusedFields.confirmPassword ? styles.inputFocus : {})
                  }}
                  placeholder="Confirm your password"
                  required
                />
                <div style={styles.inputIcon}>
                  <i className="fas fa-lock"></i>
                </div>
              </div>
            </div>

            {/* Register Button */}
            <div style={styles.registerButtonContainer}>
              <button
                type="submit"
                disabled={isLoading}
                style={{
                  ...styles.registerButton,
                  ...(buttonHovered && !isLoading ? styles.registerButtonHover : {}),
                  ...(isLoading ? styles.registerButtonDisabled : {})
                }}
                onMouseEnter={() => setButtonHovered(true)}
                onMouseLeave={() => setButtonHovered(false)}
              >
                {isLoading ? (
                  <>
                    <i className="fas fa-spinner fa-spin"></i>
                    Creating Account...
                  </>
                ) : (
                  <>
                    <span>Create Account</span>
                    <i className="fas fa-user-plus"></i>
                  </>
                )}
              </button>
            </div>

            {/* Message Display */}
            {message && (
              <div style={styles.messageContainer}>
                <div style={message.includes('successful') ? styles.successMessage : styles.errorMessage}>
                  {message}
                </div>
              </div>
            )}
          </form>

          {/* Login Link */}
          <div style={styles.loginSection}>
            <p style={styles.loginText}>
              Already have an account?
              <span
                style={{
                  ...styles.loginLink,
                  ...(loginHovered ? styles.loginLinkHover : {})
                }}
                onMouseEnter={() => setLoginHovered(true)}
                onMouseLeave={() => setLoginHovered(false)}
                onClick={() => navigate('/login')}
              >
                Sign in
              </span>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Register;
