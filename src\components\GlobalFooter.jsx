import React from 'react';

function GlobalFooter() {
  return (
    <footer className="bg-gradient-to-br from-[#006D77] to-[#004d56] text-white py-8 sm:py-10 md:py-12 lg:py-14 xl:py-16 mt-8 w-screen relative left-1/2 right-1/2 -ml-[50vw] -mr-[50vw]">
      <div className="max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8 xl:px-8 w-full">
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-8 sm:gap-10 md:gap-12 lg:gap-16 mb-8">
          {/* Brand Section */}
          <div className="text-center sm:col-span-2 md:col-span-1 md:text-left">
            <h3 className="text-xl md:text-2xl font-bold font-['Poppins'] mb-3 text-white">
              LiberateBites
            </h3>
            <p className="text-white/80 mb-6 text-sm md:text-base leading-relaxed">
              Nourishing body and mind with mood-enhancing vegetarian cuisine.
            </p>
            <div className="flex justify-center md:justify-start gap-4 mb-4">
              <div className="w-10 h-10 lg:w-11 lg:h-11 bg-white/10 rounded-full flex items-center justify-center text-white hover:bg-yellow-200/20 hover:text-yellow-200 hover:-translate-y-0.5 transition-all duration-300 cursor-pointer">
                <i className="fa-brands fa-instagram"></i>
              </div>
              <div className="w-10 h-10 lg:w-11 lg:h-11 bg-white/10 rounded-full flex items-center justify-center text-white hover:bg-yellow-200/20 hover:text-yellow-200 hover:-translate-y-0.5 transition-all duration-300 cursor-pointer">
                <i className="fa-brands fa-facebook"></i>
              </div>
              <div className="w-10 h-10 lg:w-11 lg:h-11 bg-white/10 rounded-full flex items-center justify-center text-white hover:bg-yellow-200/20 hover:text-yellow-200 hover:-translate-y-0.5 transition-all duration-300 cursor-pointer">
                <i className="fa-brands fa-twitter"></i>
              </div>
              <div className="w-10 h-10 lg:w-11 lg:h-11 bg-white/10 rounded-full flex items-center justify-center text-white hover:bg-yellow-200/20 hover:text-yellow-200 hover:-translate-y-0.5 transition-all duration-300 cursor-pointer">
                <i className="fa-brands fa-youtube"></i>
              </div>
            </div>
          </div>

          {/* Quick Links Section */}
          <div className="text-center sm:text-left">
            <h4 className="text-base md:text-lg font-semibold mb-4 text-white">
              Quick Links
            </h4>
            <ul className="space-y-2">
              <li>
                <span className="text-white/80 text-sm md:text-base hover:text-yellow-200 transition-colors duration-300 cursor-pointer py-1 block">
                  About Us
                </span>
              </li>
              <li>
                <span className="text-white/80 text-sm md:text-base hover:text-yellow-200 transition-colors duration-300 cursor-pointer py-1 block">
                  Our Menu
                </span>
              </li>
              <li>
                <span className="text-white/80 text-sm md:text-base hover:text-yellow-200 transition-colors duration-300 cursor-pointer py-1 block">
                  Mood Quiz
                </span>
              </li>
              <li>
                <span className="text-white/80 text-sm md:text-base hover:text-yellow-200 transition-colors duration-300 cursor-pointer py-1 block">
                  Contact
                </span>
              </li>
            </ul>
          </div>

          {/* Support Section */}
          <div className="text-center sm:text-left">
            <h4 className="text-base md:text-lg font-semibold mb-4 text-white">
              Support
            </h4>
            <ul className="space-y-2">
              <li>
                <span className="text-white/80 text-sm md:text-base hover:text-yellow-200 transition-colors duration-300 cursor-pointer py-1 block">
                  Help Center
                </span>
              </li>
              <li>
                <span className="text-white/80 text-sm md:text-base hover:text-yellow-200 transition-colors duration-300 cursor-pointer py-1 block">
                  Privacy Policy
                </span>
              </li>
              <li>
                <span className="text-white/80 text-sm md:text-base hover:text-yellow-200 transition-colors duration-300 cursor-pointer py-1 block">
                  Terms of Service
                </span>
              </li>
              <li>
                <span className="text-white/80 text-sm md:text-base hover:text-yellow-200 transition-colors duration-300 cursor-pointer py-1 block">
                  FAQ
                </span>
              </li>
            </ul>
          </div>

          {/* Newsletter Section */}
          <div className="text-center sm:text-left">
            <h4 className="text-base md:text-lg font-semibold mb-4 text-white">
              Stay Updated
            </h4>
            <p className="text-white/80 mb-4 text-xs md:text-sm leading-relaxed">
              Subscribe to our newsletter for mood-boosting recipes and wellness tips.
            </p>
            <form
              className="flex flex-col sm:flex-row gap-3 max-w-xs sm:max-w-none mx-auto sm:mx-0"
              onSubmit={(e) => e.preventDefault()}
            >
              <input
                type="email"
                placeholder="Enter your email"
                className="flex-1 px-3 py-3 rounded-lg text-sm bg-white text-gray-700 placeholder-gray-400 border-none outline-none"
                required
              />
              <button
                type="submit"
                className="bg-[#FF6F61] hover:bg-[#e55a4d] hover:-translate-y-0.5 active:scale-95 px-4 py-3 rounded-lg text-white font-medium min-h-[44px] flex items-center justify-center gap-2 transition-all duration-300"
              >
                <i className="fa-solid fa-paper-plane"></i>
                <span>Subscribe</span>
              </button>
            </form>
          </div>
        </div>

        <div className="border-t border-white/20 pt-6 text-center">
          <p className="text-white/60 text-xs md:text-sm leading-relaxed m-0">
            © {new Date().getFullYear()} LiberateBites. All rights reserved. Nourish your mood, feed your soul.
          </p>
        </div>
      </div>
    </footer>
  );
}

export default GlobalFooter;
