import React from 'react';

function GlobalFooter() {
  return (
    <>
      <style>
        {`
          /* Mobile-first responsive footer design */

          .footer-container {
            background: linear-gradient(135deg, #006D77 0%, #004d56 100%);
            color: white;
            padding: 2rem 0 1.5rem 0;
            margin: 2rem 0 0 0;
            width: 100vw;
            position: relative;
            left: 50%;
            right: 50%;
            margin-left: -50vw;
            margin-right: -50vw;
            box-sizing: border-box;
            overflow-x: hidden;
          }

          /* Ensure body doesn't have horizontal scroll */
          body {
            overflow-x: hidden;
          }

          /* Alternative approach for full width on mobile */
          @media (max-width: 768px) {
            .footer-container {
              width: 100vw;
              margin-left: calc(-50vw + 50%);
              margin-right: calc(-50vw + 50%);
              left: 0;
              right: 0;
              position: relative;
              max-width: none;
            }
          }

          /* Ensure no parent containers interfere */
          .footer-container {
            min-width: 100vw;
          }

          /* Fix for containers that might have padding */
          @media (max-width: 640px) {
            .footer-container {
              margin-left: calc(-50vw + 50%) !important;
              margin-right: calc(-50vw + 50%) !important;
              width: 100vw !important;
              max-width: 100vw !important;
            }
          }

          .footer-content {
            max-width: 1280px;
            margin: 0 auto;
            padding: 0 1rem;
            width: 100%;
            box-sizing: border-box;
          }

          .footer-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
          }

          .footer-section {
            text-align: center;
          }

          .footer-brand h3 {
            font-size: 1.375rem;
            font-family: 'Poppins', sans-serif;
            font-weight: 700;
            margin: 0 0 0.75rem 0;
            color: white;
          }

          .footer-brand p {
            color: rgba(255, 255, 255, 0.8);
            margin: 0 0 1.5rem 0;
            font-size: 0.875rem;
            line-height: 1.5;
          }

          .social-links {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 1rem;
          }

          .social-link {
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
            cursor: pointer;
          }

          .social-link:hover {
            background: rgba(255, 243, 176, 0.2);
            color: #FFF3B0;
            transform: translateY(-2px);
          }

          .footer-section h4 {
            font-size: 1rem;
            font-weight: 600;
            margin: 0 0 1rem 0;
            color: white;
          }

          .footer-links {
            list-style: none;
            padding: 0;
            margin: 0;
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
          }

          .footer-link {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            font-size: 0.875rem;
            transition: color 0.3s ease;
            cursor: pointer;
            padding: 0.25rem 0;
          }

          .footer-link:hover {
            color: #FFF3B0;
          }

          .newsletter-section p {
            color: rgba(255, 255, 255, 0.8);
            margin: 0 0 1rem 0;
            font-size: 0.8125rem;
            line-height: 1.4;
          }

          .newsletter-form {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
            max-width: 300px;
            margin: 0 auto;
          }

          .newsletter-input {
            padding: 0.75rem;
            border: none;
            border-radius: 0.5rem;
            font-size: 0.875rem;
            outline: none;
            background: white;
            color: #374151;
          }

          .newsletter-input::placeholder {
            color: #9ca3af;
          }

          .newsletter-button {
            background: #FF6F61;
            border: none;
            border-radius: 0.5rem;
            padding: 0.75rem;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
            min-height: 44px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
          }

          .newsletter-button:hover {
            background: #e55a4d;
            transform: translateY(-1px);
          }

          .newsletter-button:active {
            transform: scale(0.98);
          }

          .footer-bottom {
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            padding-top: 1.5rem;
            text-align: center;
            color: rgba(255, 255, 255, 0.6);
            font-size: 0.75rem;
            line-height: 1.4;
          }

          .footer-bottom p {
            margin: 0;
          }

          /* Improved responsive breakpoints using Tailwind-like approach */

          /* Small Mobile Styles (max-width: 479px) */
          @media (max-width: 479px) {
            .footer-content {
              padding: 0 0.75rem;
            }

            .footer-container {
              padding: 1.5rem 0 1rem 0;
            }

            .footer-grid {
              gap: 1.5rem;
            }

            .footer-brand h3 {
              font-size: 1.25rem;
            }

            .footer-brand p {
              font-size: 0.8125rem;
            }

            .social-link {
              width: 36px;
              height: 36px;
            }
          }

          /* Mobile Styles (480px-639px) */
          @media (min-width: 480px) and (max-width: 639px) {
            .footer-content {
              padding: 0 1rem;
            }

            .footer-container {
              padding: 1.75rem 0 1.25rem 0;
            }
          }

          /* Tablet Styles (640px+) */
          @media (min-width: 640px) {
            .footer-container {
              padding: 2.5rem 0 2rem 0;
            }

            .footer-content {
              padding: 0 1rem;
            }

            .footer-grid {
              grid-template-columns: repeat(2, 1fr);
              gap: 2.5rem;
            }

            .footer-section {
              text-align: left;
            }

            .footer-brand {
              text-align: center;
              grid-column: 1 / -1;
            }

            .social-links {
              justify-content: center;
            }

            .newsletter-form {
              flex-direction: row;
              max-width: none;
            }

            .newsletter-input {
              flex: 1;
            }

            .newsletter-button {
              width: auto;
              padding: 0.75rem 1rem;
              white-space: nowrap;
            }

            .footer-bottom {
              font-size: 0.8125rem;
            }
          }

          /* Desktop Styles (768px+) */
          @media (min-width: 768px) {
            .footer-container {
              padding: 3rem 0 2rem 0;
            }

            .footer-content {
              padding: 0 1.5rem;
            }

            .footer-grid {
              grid-template-columns: 2fr 1fr 1fr 1.5fr;
              gap: 3rem;
            }

            .footer-brand {
              text-align: left;
              grid-column: auto;
            }

            .social-links {
              justify-content: flex-start;
            }

            .footer-brand h3 {
              font-size: 1.5rem;
            }

            .footer-brand p {
              font-size: 0.9375rem;
            }

            .footer-section h4 {
              font-size: 1.125rem;
            }

            .footer-link {
              font-size: 0.9375rem;
            }

            .newsletter-section p {
              font-size: 0.875rem;
            }

            .footer-bottom {
              font-size: 0.875rem;
            }
          }

          /* Large Desktop Styles (1024px+) */
          @media (min-width: 1024px) {
            .footer-container {
              padding: 3.5rem 0 2.5rem 0;
            }

            .footer-content {
              padding: 0 2rem;
            }

            .footer-grid {
              gap: 4rem;
            }

            .social-link {
              width: 44px;
              height: 44px;
            }
          }

          /* Extra Large Screens (1280px+) */
          @media (min-width: 1280px) {
            .footer-container {
              padding: 4rem 0 3rem 0;
            }

            .footer-content {
              padding: 0 2rem;
            }
          }

          /* Additional mobile-specific improvements */
          @media (max-width: 640px) {
            .newsletter-form {
              width: 100%;
            }

            .newsletter-input {
              width: 100%;
              box-sizing: border-box;
            }

            .newsletter-button {
              width: 100%;
              box-sizing: border-box;
            }
          }

          /* Ensure proper text wrapping on small screens */
          @media (max-width: 480px) {
            .footer-brand p,
            .newsletter-section p {
              word-wrap: break-word;
              hyphens: auto;
            }

            .footer-bottom p {
              word-wrap: break-word;
              line-height: 1.5;
            }
          }
        `}
      </style>

      <footer className="footer-container">
        <div className="footer-content">
          <div className="footer-grid">
            {/* Brand Section */}
            <div className="footer-section footer-brand">
              <h3>LiberateBites</h3>
              <p>Nourishing body and mind with mood-enhancing vegetarian cuisine.</p>
              <div className="social-links">
                <div className="social-link">
                  <i className="fa-brands fa-instagram"></i>
                </div>
                <div className="social-link">
                  <i className="fa-brands fa-facebook"></i>
                </div>
                <div className="social-link">
                  <i className="fa-brands fa-twitter"></i>
                </div>
                <div className="social-link">
                  <i className="fa-brands fa-youtube"></i>
                </div>
              </div>
            </div>

            {/* Quick Links Section */}
            <div className="footer-section">
              <h4>Quick Links</h4>
              <ul className="footer-links">
                <li><span className="footer-link">About Us</span></li>
                <li><span className="footer-link">Our Menu</span></li>
                <li><span className="footer-link">Mood Quiz</span></li>
                <li><span className="footer-link">Contact</span></li>
              </ul>
            </div>

            {/* Support Section */}
            <div className="footer-section">
              <h4>Support</h4>
              <ul className="footer-links">
                <li><span className="footer-link">Help Center</span></li>
                <li><span className="footer-link">Privacy Policy</span></li>
                <li><span className="footer-link">Terms of Service</span></li>
                <li><span className="footer-link">FAQ</span></li>
              </ul>
            </div>

            {/* Newsletter Section */}
            <div className="footer-section newsletter-section">
              <h4>Stay Updated</h4>
              <p>Subscribe to our newsletter for mood-boosting recipes and wellness tips.</p>
              <form className="newsletter-form" onSubmit={(e) => e.preventDefault()}>
                <input
                  type="email"
                  placeholder="Enter your email"
                  className="newsletter-input"
                  required
                />
                <button type="submit" className="newsletter-button">
                  <i className="fa-solid fa-paper-plane"></i>
                  <span>Subscribe</span>
                </button>
              </form>
            </div>
          </div>

          <div className="footer-bottom">
            <p>© {new Date().getFullYear()} LiberateBites. All rights reserved. Nourish your mood, feed your soul.</p>
          </div>
        </div>
      </footer>
    </>
  );
}

export default GlobalFooter;
