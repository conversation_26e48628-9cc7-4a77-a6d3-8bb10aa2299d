<script type="text/javascript">
        var gk_isXlsx = false;
        var gk_xlsxFileLookup = {};
        var gk_fileData = {};
        function filledCell(cell) {
          return cell !== '' && cell != null;
        }
        function loadFileData(filename) {
        if (gk_isXlsx && gk_xlsxFileLookup[filename]) {
            try {
                var workbook = XLSX.read(gk_fileData[filename], { type: 'base64' });
                var firstSheetName = workbook.SheetNames[0];
                var worksheet = workbook.Sheets[firstSheetName];

                // Convert sheet to JSON to filter blank rows
                var jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1, blankrows: false, defval: '' });
                // Filter out blank rows (rows where all cells are empty, null, or undefined)
                var filteredData = jsonData.filter(row => row.some(filledCell));

                // Heuristic to find the header row by ignoring rows with fewer filled cells than the next row
                var headerRowIndex = filteredData.findIndex((row, index) =>
                  row.filter(filledCell).length >= filteredData[index + 1]?.filter(filledCell).length
                );
                // Fallback
                if (headerRowIndex === -1 || headerRowIndex > 25) {
                  headerRowIndex = 0;
                }

                // Convert filtered JSON back to CSV
                var csv = XLSX.utils.aoa_to_sheet(filteredData.slice(headerRowIndex)); // Create a new sheet from filtered array of arrays
                csv = XLSX.utils.sheet_to_csv(csv, { header: 1 });
                return csv;
            } catch (e) {
                console.error(e);
                return "";
            }
        }
        return gk_fileData[filename] || "";
        }
        </script><html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="https://cdn.tailwindcss.com"></script>
    <script> window.FontAwesomeConfig = { autoReplaceSvg: 'nest'};</script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="https://cdn.jsdelivr.net/npm/gsap@3.12.2/dist/gsap.min.js"></script>
    
    <style>::-webkit-scrollbar { display: none;}</style>
    <script>tailwind.config = {
  "theme": {
    "extend": {
      "colors": {
        "coral": {
          "DEFAULT": "#FF6F61",
          "light": "#FF8C82",
          "dark": "#E56459"
        },
        "teal": {
          "DEFAULT": "#006D77",
          "light": "#0A9DAD",
          "dark": "#005A62"
        },
        "cream": "#FFF8EE",
        "peach": "#FFDFD1"
      },
      "fontFamily": {
        "poppins": [
          "Poppins",
          "sans-serif"
        ],
        "sans": [
          "Inter",
          "sans-serif"
        ]
      },
      "animation": {
        "float": "float 6s ease-in-out infinite",
        "float-delay": "float 6s ease-in-out 2s infinite",
        "pulse-slow": "pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite"
      },
      "keyframes": {
        "float": {
          "0%, 100%": {
            "transform": "translateY(0)"
          },
          "50%": {
            "transform": "translateY(-10px)"
          }
        }
      }
    }
  }
};</script>
<link rel="preconnect" href="https://fonts.googleapis.com"><link rel="preconnect" href="https://fonts.gstatic.com" crossorigin=""><link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;500;600;700;800;900&amp;display=swap"><style>
      body {
        font-family: 'Inter', sans-serif !important;
      }
      
      /* Preserve Font Awesome icons */
      .fa, .fas, .far, .fal, .fab {
        font-family: "Font Awesome 6 Free", "Font Awesome 6 Brands" !important;
      }
    </style><style>
  .highlighted-section {
    outline: 2px solid #3F20FB;
    background-color: rgba(63, 32, 251, 0.1);
  }

  .edit-button {
    position: absolute;
    z-index: 1000;
  }

  ::-webkit-scrollbar {
    display: none;
  }

  html, body {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  </style>
</head>
<body class="font-poppins bg-gradient-to-b from-cream to-peach min-h-screen flex items-center justify-center p-4">
    <div id="particles-container" class="fixed inset-0 pointer-events-none">
        <div class="absolute top-1/4 left-1/5 w-4 h-4 rounded-full bg-coral opacity-20 animate-float"></div>
        <div class="absolute top-1/3 right-1/4 w-6 h-6 rounded-full bg-teal opacity-10 animate-float-delay"></div>
        <div class="absolute bottom-1/4 left-1/3 w-5 h-5 rounded-full bg-coral opacity-15 animate-float"></div>
        <div class="absolute top-2/3 right-1/3 w-3 h-3 rounded-full bg-teal opacity-20 animate-float-delay"></div>
        <div class="absolute bottom-1/3 right-1/5 w-4 h-4 rounded-full bg-coral opacity-10 animate-float"></div>
    </div>
    
    <div id="quiz-container" class="w-full max-w-[600px] bg-white bg-opacity-90 backdrop-blur-sm rounded-3xl shadow-xl p-8 relative overflow-hidden">
        <!-- Header with Progress -->
        <div id="quiz-header" class="mb-8">
            <div class="flex items-center justify-between mb-2">
                <div class="text-sm font-medium text-teal">
                    <span id="current-step">1</span>/<span id="total-steps">5</span>
                </div>
                <div class="text-sm font-medium text-teal flex items-center">
                    <i class="fa-regular fa-clock mr-2"></i>
                    <span id="time-remaining">2 mins left</span>
                </div>
            </div>
            <div class="h-2 w-full bg-gray-200 rounded-full overflow-hidden">
                <div id="progress-bar" class="h-full w-1/5 bg-coral rounded-full transition-all duration-500 ease-out"></div>
            </div>
        </div>
        
        <!-- Quiz Content -->
        <div id="quiz-content" class="transition-all duration-500 ease-out">
            <h1 class="text-3xl md:text-4xl font-bold text-teal-dark text-center mb-4">How are you feeling today?</h1>
            <p class="text-center text-gray-600 mb-10">We'll match your mood with personalized food recommendations to lift your spirits and nourish your body.</p>
            
            <!-- Mood Options -->
            <div id="mood-options" class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
                <button id="mood-1" class="mood-option flex flex-col items-center p-6 bg-white rounded-2xl border-2 border-gray-200 transition-all duration-300 hover:border-coral hover:shadow-md focus:outline-none focus:ring-2 focus:ring-coral focus:ring-offset-2">
                    <span class="text-5xl mb-4">😊</span>
                    <span class="font-medium text-gray-800">Happy</span>
                </button>
                
                <button id="mood-2" class="mood-option flex flex-col items-center p-6 bg-white rounded-2xl border-2 border-gray-200 transition-all duration-300 hover:border-coral hover:shadow-md focus:outline-none focus:ring-2 focus:ring-coral focus:ring-offset-2">
                    <span class="text-5xl mb-4">😔</span>
                    <span class="font-medium text-gray-800">Sad</span>
                </button>
                
                <button id="mood-3" class="mood-option flex flex-col items-center p-6 bg-white rounded-2xl border-2 border-gray-200 transition-all duration-300 hover:border-coral hover:shadow-md focus:outline-none focus:ring-2 focus:ring-coral focus:ring-offset-2">
                    <span class="text-5xl mb-4">😤</span>
                    <span class="font-medium text-gray-800">Stressed</span>
                </button>
                
                <button id="mood-4" class="mood-option flex flex-col items-center p-6 bg-white rounded-2xl border-2 border-gray-200 transition-all duration-300 hover:border-coral hover:shadow-md focus:outline-none focus:ring-2 focus:ring-coral focus:ring-offset-2">
                    <span class="text-5xl mb-4">😴</span>
                    <span class="font-medium text-gray-800">Tired</span>
                </button>
                
                <button id="mood-5" class="mood-option md:col-span-2 flex flex-col items-center p-6 bg-white rounded-2xl border-2 border-gray-200 transition-all duration-300 hover:border-coral hover:shadow-md focus:outline-none focus:ring-2 focus:ring-coral focus:ring-offset-2">
                    <span class="text-5xl mb-4">😐</span>
                    <span class="font-medium text-gray-800">Neutral</span>
                </button>
            </div>
        </div>
        
        <!-- Navigation Buttons -->
        <div id="navigation-buttons" class="flex justify-between mt-8">
            <button id="prev-button" class="px-6 py-3 rounded-full text-teal border-2 border-teal transition-all duration-300 hover:bg-teal hover:text-white focus:outline-none focus:ring-2 focus:ring-teal focus:ring-offset-2 disabled:opacity-50" disabled="">
                <i class="fa-solid fa-arrow-left mr-2"></i>Previous
            </button>
            
            <button id="skip-button" class="px-6 py-3 rounded-full text-gray-500 border-2 border-gray-300 transition-all duration-300 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-300 focus:ring-offset-2">
                Skip
                <i class="fa-solid fa-forward ml-2"></i>
            </button>
            
            <button id="next-button" class="px-6 py-3 rounded-full bg-coral text-white transition-all duration-300 hover:bg-coral-dark focus:outline-none focus:ring-2 focus:ring-coral focus:ring-offset-2">
                Next
                <i class="fa-solid fa-arrow-right ml-2"></i>
            </button>
        </div>
    </div>
    
    <script>
        // Initialize the quiz
        document.addEventListener('DOMContentLoaded', function() {
            // Variables
            const totalSteps = 5;
            const currentStepEl = document.getElementById('current-step');
            const totalStepsEl = document.getElementById('total-steps');
            const progressBar = document.getElementById('progress-bar');
            const prevButton = document.getElementById('prev-button');
            const nextButton = document.getElementById('next-button');
            const skipButton = document.getElementById('skip-button');
            const moodOptions = document.querySelectorAll('.mood-option');
            
            let currentStep = 1;
            let selectedMood = null;
            
            // Initialize the quiz
            totalStepsEl.textContent = totalSteps;
            updateProgress();
            
            // Event Listeners
            moodOptions.forEach(option => {
                option.addEventListener('click', function() {
                    // Remove selection from all options
                    moodOptions.forEach(opt => {
                        opt.classList.remove('border-coral', 'bg-coral-light', 'bg-opacity-10');
                    });
                    
                    // Add selection to clicked option
                    this.classList.add('border-coral', 'bg-coral-light', 'bg-opacity-10');
                    selectedMood = this.id;
                    
                    // Animate the button
                    gsap.to(this, {
                        scale: 1.05,
                        duration: 0.2,
                        yoyo: true,
                        repeat: 1
                    });
                });
            });
            
            nextButton.addEventListener('click', function() {
                if (currentStep < totalSteps) {
                    currentStep++;
                    updateProgress();
                    animateTransition();
                }
            });
            
            prevButton.addEventListener('click', function() {
                if (currentStep > 1) {
                    currentStep--;
                    updateProgress();
                    animateTransition();
                }
            });
            
            skipButton.addEventListener('click', function() {
                if (currentStep < totalSteps) {
                    currentStep++;
                    updateProgress();
                    animateTransition();
                }
            });
            
            // Functions
            function updateProgress() {
                currentStepEl.textContent = currentStep;
                progressBar.style.width = `${(currentStep / totalSteps) * 100}%`;
                
                // Enable/disable prev button
                prevButton.disabled = currentStep === 1;
                
                // Update button states
                if (currentStep === 1) {
                    prevButton.classList.add('opacity-50', 'cursor-not-allowed');
                } else {
                    prevButton.classList.remove('opacity-50', 'cursor-not-allowed');
                }
                
                // Update time remaining text based on steps left
                const timeLeft = (totalSteps - currentStep + 1) * 30; // 30 seconds per question
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                
                document.getElementById('time-remaining').textContent = 
                    `${minutes > 0 ? minutes + ' min' : ''} ${seconds > 0 ? seconds + ' sec' : ''} left`;
            }
            
            function animateTransition() {
                const content = document.getElementById('quiz-content');
                
                gsap.to(content, {
                    opacity: 0,
                    y: -20,
                    duration: 0.3,
                    onComplete: function() {
                        // This would be where you'd load the next question content
                        gsap.to(content, {
                            opacity: 1,
                            y: 0,
                            duration: 0.3
                        });
                    }
                });
            }
            
            // Initialize floating particles animation
            const particles = document.querySelectorAll('#particles-container > div');
            particles.forEach(particle => {
                gsap.to(particle, {
                    y: '-=20',
                    duration: gsap.utils.random(4, 8),
                    repeat: -1,
                    yoyo: true,
                    ease: 'sine.inOut',
                    delay: gsap.utils.random(0, 5)
                });
            });
        });
    </script>
</body>
</html>