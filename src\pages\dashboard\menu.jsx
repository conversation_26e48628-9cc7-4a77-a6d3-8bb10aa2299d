import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { foodItemsAPI, moodsAPI } from '../../services/api';
import DashboardSidebar from '../../components/DashboardSidebar';

function Menu() {
  const navigate = useNavigate();
  const [foodItems, setFoodItems] = useState([]);
  const [moods, setMoods] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedMood, setSelectedMood] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [userName, setUserName] = useState('Aisha');

  // CRUD form states
  const [showCrudForm, setShowCrudForm] = useState(false);
  const [formMode, setFormMode] = useState('add'); // 'add', 'edit'
  const [editingFoodItem, setEditingFoodItem] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    type: 'veg', // 'veg' or 'non-veg' as per API
    image: '',
    mood_id: '',
    price: '',
    category: ''
  });
  const [formLoading, setFormLoading] = useState(false);
  const [formMessage, setFormMessage] = useState('');

  // Form field focus states
  const [nameFocused, setNameFocused] = useState(false);
  const [descriptionFocused, setDescriptionFocused] = useState(false);
  const [imageFocused, setImageFocused] = useState(false);

  // Form field hover states
  const [nameHovered, setNameHovered] = useState(false);
  const [submitHovered, setSubmitHovered] = useState(false);
  const [cancelHovered, setCancelHovered] = useState(false);

  // Mood categories for filtering
  const moodCategories = [
    { id: 'all', name: 'All Food Items', color: '#6b7280' },
    { id: 'stressed', name: 'Feeling Stressed', color: '#ef4444' },
    { id: 'energy', name: 'Need Energy', color: '#eab308' },
    { id: 'comfort', name: 'Seeking Comfort', color: '#a855f7' },
    { id: 'focus', name: 'Need Focus', color: '#3b82f6' },
    { id: 'calm', name: 'Want Calm', color: '#10b981' }
  ];

  useEffect(() => {
    // Get user data from localStorage
    const storedUser = localStorage.getItem('user');
    if (storedUser) {
      try {
        const userData = JSON.parse(storedUser);
        if (userData && userData.name) {
          setUserName(userData.name);
        }
      } catch (error) {
        console.error('Error parsing user data:', error);
      }
    }

    fetchFoodItems();
    fetchMoods();
  }, []);

  const fetchFoodItems = async () => {
    try {
      setLoading(true);
      const data = await foodItemsAPI.getAll();
      console.log('Food items data received:', data);
      setFoodItems(data);
      setError('');
    } catch (error) {
      console.error('Error fetching food items:', error);
      setError(error.message || 'Error loading food items');
    } finally {
      setLoading(false);
    }
  };

  const fetchMoods = async () => {
    try {
      const data = await moodsAPI.getAll();
      console.log('Moods data received:', data);
      setMoods(data);
    } catch (error) {
      console.error('Error fetching moods:', error);
      setMoods([]);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setFormLoading(true);
    setFormMessage('');

    try {
      if (formMode === 'add') {
        await foodItemsAPI.create(formData);
        setFormMessage('Food item added successfully!');
      } else {
        await foodItemsAPI.update(editingFoodItem.id, formData);
        setFormMessage('Food item updated successfully!');
      }

      // Refresh food items list
      await fetchFoodItems();

      // Reset form after short delay
      setTimeout(() => {
        resetForm();
        setShowCrudForm(false);
      }, 1500);
    } catch (error) {
      console.error('Error saving food item:', error);
      setFormMessage(error.message || 'Error saving food item');
    } finally {
      setFormLoading(false);
    }
  };

  const handleDeleteFoodItem = async (foodItemId) => {
    if (window.confirm('Are you sure you want to delete this food item?')) {
      try {
        await foodItemsAPI.delete(foodItemId);
        await fetchFoodItems(); // Refresh the list
      } catch (error) {
        console.error('Error deleting food item:', error);
        alert(error.message || 'Error deleting food item');
      }
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      type: 'veg',
      image: '',
      mood_id: '',
      price: '',
      category: ''
    });
    setEditingFoodItem(null);
    setFormMode('add');
    setFormMessage('');
  };

  // Filter food items based on search term and selected mood
  const filteredFoodItems = foodItems.filter(foodItem => {
    const matchesSearch = !searchTerm ||
                         (foodItem.name || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (foodItem.description || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (foodItem.type || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (foodItem.category || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (foodItem.price || '').toString().includes(searchTerm.toLowerCase());

    if (selectedMood === 'all') return matchesSearch;

    const matchesMood = foodItem.mood && (
      (foodItem.mood.name || '').toLowerCase().includes(selectedMood) ||
      selectedMood.includes((foodItem.mood.name || '').toLowerCase())
    );

    return matchesSearch && matchesMood;
  });

  const getMoodColor = (moodName) => {
    if (!moodName || typeof moodName !== 'string') {
      return '#6b7280';
    }
    const mood = moodCategories.find(m =>
      moodName.toLowerCase().includes(m.id) ||
      m.name.toLowerCase().includes(moodName.toLowerCase())
    );
    return mood ? mood.color : '#6b7280';
  };

  // CRUD Functions
  const handleAdd = () => {
    setFormMode('add');
    setEditingFoodItem(null);
    setFormData({
      name: '',
      description: '',
      type: 'veg',
      image: '',
      mood_id: '',
      price: '',
      category: ''
    });
    setFormMessage('');
    setShowCrudForm(true);
  };

  const handleEditFoodItem = (foodItem) => {
    setFormMode('edit');
    setEditingFoodItem(foodItem);
    setFormData({
      name: foodItem.name || '',
      description: foodItem.description || '',
      type: foodItem.type || 'veg',
      image: foodItem.image || '',
      mood_id: foodItem.mood_id || '',
      price: foodItem.price || '',
      category: foodItem.category || ''
    });
    setFormMessage('');
    setShowCrudForm(true);
  };

  const handleFormCancel = () => {
    setShowCrudForm(false);
    setFormData({
      name: '',
      description: '',
      type: 'veg',
      image: '',
      mood_id: '',
      price: '',
      category: ''
    });
    setFormMessage('');
  };

  const openAddForm = () => handleAdd();
  const openEditForm = (foodItem) => handleEditFoodItem(foodItem);
  const closeCrudForm = () => handleFormCancel();

  return (
    <div style={{ backgroundColor: '#F5E8C7', fontFamily: 'Inter, sans-serif', minHeight: '100vh' }}>
      {/* Dashboard Sidebar */}
      <DashboardSidebar />

      {/* Main Content */}
      <div style={{ marginLeft: '256px', minHeight: '100vh' }}>
        {/* Content Area */}
        <div style={{ padding: '32px' }}>
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 300px', gap: '32px', marginBottom: '32px' }}>
            <div>
              <div style={{
                backgroundColor: 'white',
                borderRadius: '12px',
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                padding: '24px'
              }}>
                <h1 style={{
                  fontSize: '28px',
                  fontWeight: '700',
                  color: '#1f2937',
                  marginBottom: '8px'
                }}>Food Menu for {userName}!</h1>
                <p style={{ color: '#4b5563', marginTop: '4px' }}>Manage food items and their mood associations</p>

                {/* Search Bar */}
                <div style={{ position: 'relative', marginTop: '16px', maxWidth: '100%' }}>
                  <input
                    type="text"
                    placeholder="Search food items by name, description, type, category or price..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    style={{
                      width: '100%',
                      maxWidth: '100%',
                      padding: '12px 40px 12px 16px',
                      border: '1px solid rgba(0, 109, 119, 0.3)',
                      borderRadius: '8px',
                      fontSize: '14px',
                      outline: 'none',
                      backgroundColor: '#f9fafb',
                      boxSizing: 'border-box'
                    }}
                  />
                  <i className="fa-solid fa-search" style={{
                    position: 'absolute',
                    right: '12px',
                    top: '50%',
                    transform: 'translateY(-50%)',
                    color: 'rgba(0, 109, 119, 0.6)'
                  }}></i>
                </div>

                {/* Mood Filter Tabs */}
                <div style={{
                  display: 'flex',
                  flexWrap: 'wrap',
                  gap: '8px',
                  marginTop: '16px'
                }}>
                  {moodCategories.map(mood => (
                    <button
                      key={mood.id}
                      onClick={() => setSelectedMood(mood.id)}
                      style={{
                        padding: '6px 12px',
                        borderRadius: '9999px',
                        border: 'none',
                        cursor: 'pointer',
                        fontSize: '12px',
                        fontWeight: '500',
                        backgroundColor: selectedMood === mood.id ? mood.color : `${mood.color}20`,
                        color: selectedMood === mood.id ? 'white' : mood.color
                      }}
                    >
                      {mood.name}
                    </button>
                  ))}
                </div>

                {/* Add New Food Item Button */}
                <div style={{ marginTop: '16px' }}>
                  <button
                    onClick={() => handleAdd()}
                    style={{
                      padding: '12px 24px',
                      backgroundColor: '#FF6F61',
                      color: 'white',
                      border: 'none',
                      borderRadius: '8px',
                      fontSize: '14px',
                      fontWeight: '500',
                      cursor: 'pointer',
                      transition: 'all 0.2s ease',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px'
                    }}
                    onMouseEnter={(e) => e.target.style.backgroundColor = 'rgba(255, 111, 97, 0.9)'}
                    onMouseLeave={(e) => e.target.style.backgroundColor = '#FF6F61'}
                  >
                    <i className="fa-solid fa-plus"></i>
                    Add New Food Item
                  </button>
                </div>
              </div>
            </div>

            <div>
              <div style={{
                backgroundColor: 'white',
                borderRadius: '12px',
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                padding: '24px'
              }}>
                <h2 style={{ fontSize: '18px', fontWeight: '600', color: '#1f2937', marginBottom: '16px' }}>Food Stats</h2>
                <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
                  <div style={{ textAlign: 'center' }}>
                    <div style={{
                      width: '48px',
                      height: '48px',
                      backgroundColor: 'rgba(0, 109, 119, 0.1)',
                      borderRadius: '50%',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      margin: '0 auto 8px'
                    }}>
                      <i className="fa-solid fa-utensils" style={{ color: '#006D77', fontSize: '20px' }}></i>
                    </div>
                    <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#1f2937' }}>{foodItems.length}</div>
                    <div style={{ fontSize: '14px', color: '#6b7280' }}>Total Food Items</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Food Items Display Section */}
          <div style={{ marginBottom: '32px' }}>
            {loading ? (
              <div style={{ textAlign: 'center', padding: '64px 0' }}>
                <div style={{
                  display: 'inline-block',
                  width: '32px',
                  height: '32px',
                  border: '3px solid #f3f4f6',
                  borderTop: '3px solid #006D77',
                  borderRadius: '50%',
                  animation: 'spin 1s linear infinite'
                }}></div>
                <p style={{ marginTop: '16px', color: '#6b7280' }}>Loading food items...</p>
              </div>
            ) : error ? (
              <div style={{
                backgroundColor: 'white',
                borderRadius: '12px',
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                padding: '48px',
                textAlign: 'center'
              }}>
                <i className="fa-solid fa-exclamation-triangle" style={{ fontSize: '32px', color: '#ef4444', marginBottom: '16px' }}></i>
                <p style={{ color: '#ef4444', fontSize: '16px', marginBottom: '16px' }}>{error}</p>
                <button
                  onClick={fetchFoodItems}
                  style={{
                    backgroundColor: '#FF6F61',
                    color: 'white',
                    padding: '12px 24px',
                    borderRadius: '8px',
                    border: 'none',
                    cursor: 'pointer',
                    fontSize: '14px',
                    fontWeight: '500'
                  }}
                >
                  Try Again
                </button>
              </div>
            ) : filteredFoodItems.length === 0 ? (
              <div style={{
                backgroundColor: 'white',
                borderRadius: '12px',
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                padding: '48px',
                textAlign: 'center'
              }}>
                <i className="fa-solid fa-utensils" style={{ fontSize: '32px', color: '#6b7280', marginBottom: '16px' }}></i>
                <p style={{ color: '#6b7280', fontSize: '16px', marginBottom: '16px' }}>
                  {searchTerm || selectedMood !== 'all' ? 'No food items match your search criteria.' : 'No food items available.'}
                </p>
                {(searchTerm || selectedMood !== 'all') && (
                  <div style={{ display: 'flex', justifyContent: 'center', gap: '12px' }}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                      <button
                        onClick={openAddForm}
                        style={{
                          padding: '8px 16px',
                          backgroundColor: '#FF6F61',
                          color: 'white',
                          border: 'none',
                          borderRadius: '8px',
                          cursor: 'pointer',
                          fontSize: '14px',
                          fontWeight: '500',
                          display: 'flex',
                          alignItems: 'center',
                          gap: '6px',
                          transition: 'background-color 0.2s ease'
                        }}
                        onMouseEnter={(e) => e.target.style.backgroundColor = 'rgba(255, 111, 97, 0.9)'}
                        onMouseLeave={(e) => e.target.style.backgroundColor = '#FF6F61'}
                      >
                        <i className="fa-solid fa-plus"></i>
                        Add Food Item
                      </button>
                      <div style={{
                        width: '1px',
                        height: '24px',
                        backgroundColor: '#e5e7eb'
                      }}></div>
                      <button
                        onClick={() => {
                          setSearchTerm('');
                          setSelectedMood('all');
                        }}
                        style={{
                          backgroundColor: '#FF6F61',
                          color: 'white',
                          padding: '12px 24px',
                          borderRadius: '8px',
                          border: 'none',
                          cursor: 'pointer',
                          fontSize: '14px',
                          fontWeight: '500'
                        }}
                      >
                        Clear Filters
                      </button>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(3, 1fr)',
                gap: '24px'
              }}>
                {filteredFoodItems.map((foodItem) => (
                  <div
                    key={foodItem.id}
                    style={{
                      backgroundColor: 'white',
                      borderRadius: '12px',
                      boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
                      border: '1px solid #e5e7eb',
                      overflow: 'hidden',
                      transition: 'all 0.2s ease',
                      cursor: 'pointer',
                      height: 'fit-content'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.transform = 'translateY(-2px)';
                      e.currentTarget.style.boxShadow = '0 8px 25px rgba(0, 0, 0, 0.15)';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.transform = 'translateY(0)';
                      e.currentTarget.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.1)';
                    }}
                  >
                    <div style={{
                      height: '120px',
                      background: foodItem.image ? `url(${foodItem.image})` : 'linear-gradient(135deg, #006D77 0%, #83C5BE 100%)',
                      backgroundSize: 'cover',
                      backgroundPosition: 'center',
                      position: 'relative',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}>
                      {!foodItem.image && (
                        <i className="fa-solid fa-utensils" style={{ fontSize: '36px', color: 'white', opacity: 0.8 }}></i>
                      )}
                    </div>

                    <div style={{ padding: '20px', flex: 1, display: 'flex', flexDirection: 'column' }}>
                      <h3 style={{
                        fontSize: '16px',
                        fontWeight: '600',
                        color: '#1f2937',
                        marginBottom: '12px',
                        lineHeight: '1.4'
                      }}>
                        {foodItem.name || 'Untitled Food Item'}
                      </h3>

                      <div style={{
                        fontSize: '14px',
                        color: '#6b7280',
                        marginBottom: '16px'
                      }}>
                        {foodItem.description || 'No description available'}
                      </div>

                      <div style={{
                        display: 'flex',
                        flexWrap: 'wrap',
                        gap: '8px',
                        marginBottom: '16px'
                      }}>
                        <span style={{
                          backgroundColor: foodItem.type === 'veg' ? '#10b981' : '#ef4444',
                          color: 'white',
                          padding: '4px 8px',
                          borderRadius: '12px',
                          fontSize: '12px',
                          fontWeight: '500'
                        }}>
                          {foodItem.type === 'veg' ? 'Vegetarian' : 'Non-Vegetarian'}
                        </span>
                        {foodItem.mood && (
                          <span style={{
                            backgroundColor: getMoodColor(foodItem.mood.name || ''),
                            color: 'white',
                            padding: '4px 8px',
                            borderRadius: '12px',
                            fontSize: '12px',
                            fontWeight: '500'
                          }}>
                            {foodItem.mood.name}
                          </span>
                        )}
                        {foodItem.category && (
                          <span style={{
                            backgroundColor: '#6b7280',
                            color: 'white',
                            padding: '4px 8px',
                            borderRadius: '12px',
                            fontSize: '12px',
                            fontWeight: '500'
                          }}>
                            {foodItem.category}
                          </span>
                        )}
                      </div>

                      {/* Price Display */}
                      {foodItem.price && (
                        <div style={{
                          fontSize: '18px',
                          fontWeight: '600',
                          color: '#059669',
                          marginBottom: '16px'
                        }}>
                          ${parseFloat(foodItem.price).toFixed(2)}
                        </div>
                      )}

                      {/* Action Buttons */}
                      <div style={{ display: 'flex', gap: '8px', marginTop: 'auto' }}>
                        <button
                          onClick={() => openEditForm(foodItem)}
                          style={{
                            flex: 1,
                            backgroundColor: '#006D77',
                            color: 'white',
                            padding: '10px 16px',
                            borderRadius: '8px',
                            border: 'none',
                            cursor: 'pointer',
                            fontSize: '14px',
                            fontWeight: '500',
                            transition: 'background-color 0.2s ease'
                          }}
                          onMouseEnter={(e) => e.target.style.backgroundColor = 'rgba(0, 109, 119, 0.9)'}
                          onMouseLeave={(e) => e.target.style.backgroundColor = '#006D77'}
                        >
                          <i className="fa-solid fa-edit" style={{ marginRight: '6px' }}></i>
                          Edit
                        </button>
                        <button
                          onClick={() => handleDeleteFoodItem(foodItem.id)}
                          style={{
                            flex: 1,
                            backgroundColor: '#dc2626',
                            color: 'white',
                            padding: '10px 16px',
                            borderRadius: '8px',
                            border: 'none',
                            cursor: 'pointer',
                            fontSize: '14px',
                            fontWeight: '500',
                            transition: 'background-color 0.2s ease'
                          }}
                          onMouseEnter={(e) => e.target.style.backgroundColor = 'rgba(220, 38, 38, 0.9)'}
                          onMouseLeave={(e) => e.target.style.backgroundColor = '#dc2626'}
                        >
                          <i className="fa-solid fa-trash" style={{ marginRight: '6px' }}></i>
                          Delete
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* CRUD Form Modal */}
      {showCrudForm && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000,
          padding: '20px'
        }}>
          <div style={{
            backgroundColor: 'white',
            borderRadius: '12px',
            padding: '32px',
            width: '100%',
            maxWidth: '600px',
            maxHeight: '90vh',
            overflowY: 'auto',
            boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1)',
            position: 'relative'
          }}>
            <h2 style={{
              fontSize: '24px',
              fontWeight: '600',
              color: '#1f2937',
              marginBottom: '24px',
              textAlign: 'center'
            }}>
              {formMode === 'add' ? 'Add New Food Item' : 'Edit Food Item'}
            </h2>

            <form onSubmit={handleSubmit}>
              {/* Name Field */}
              <div style={{ marginBottom: '24px' }}>
                <label style={{
                  display: 'block',
                  fontSize: '14px',
                  fontWeight: '500',
                  color: '#374151',
                  marginBottom: '8px'
                }}>
                  Food Name *
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData({...formData, name: e.target.value})}
                  onFocus={() => setNameFocused(true)}
                  onBlur={() => setNameFocused(false)}
                  onMouseEnter={() => setNameHovered(true)}
                  onMouseLeave={() => setNameHovered(false)}
                  required
                  style={{
                    width: '100%',
                    padding: '12px 16px',
                    border: `2px solid ${nameFocused ? '#006D77' : (nameHovered ? 'rgba(0, 109, 119, 0.3)' : '#e5e7eb')}`,
                    borderRadius: '8px',
                    fontSize: '14px',
                    outline: 'none',
                    transition: 'all 0.2s ease',
                    backgroundColor: nameFocused ? '#f0fdfa' : 'white',
                    boxSizing: 'border-box'
                  }}
                  placeholder="Enter food item name (e.g., Grilled Chicken Salad)"
                />
              </div>

              {/* Description Field */}
              <div style={{ marginBottom: '24px' }}>
                <label style={{
                  display: 'block',
                  fontSize: '14px',
                  fontWeight: '500',
                  color: '#374151',
                  marginBottom: '8px'
                }}>
                  Description
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData({...formData, description: e.target.value})}
                  onFocus={() => setDescriptionFocused(true)}
                  onBlur={() => setDescriptionFocused(false)}
                  rows={3}
                  style={{
                    width: '100%',
                    padding: '12px 16px',
                    border: `2px solid ${descriptionFocused ? '#006D77' : '#e5e7eb'}`,
                    borderRadius: '8px',
                    fontSize: '14px',
                    outline: 'none',
                    transition: 'all 0.2s ease',
                    backgroundColor: descriptionFocused ? '#f0fdfa' : 'white',
                    boxSizing: 'border-box',
                    resize: 'vertical'
                  }}
                  placeholder="Enter food item description"
                />
              </div>

              {/* Type Field */}
              <div style={{ marginBottom: '24px' }}>
                <label style={{
                  display: 'block',
                  fontSize: '14px',
                  fontWeight: '500',
                  color: '#374151',
                  marginBottom: '8px'
                }}>
                  Food Type *
                </label>
                <select
                  value={formData.type}
                  onChange={(e) => setFormData({...formData, type: e.target.value})}
                  required
                  style={{
                    width: '100%',
                    padding: '12px 16px',
                    border: '2px solid #e5e7eb',
                    borderRadius: '8px',
                    fontSize: '14px',
                    outline: 'none',
                    transition: 'all 0.2s ease',
                    backgroundColor: 'white',
                    boxSizing: 'border-box'
                  }}
                >
                  <option value="veg">Vegetarian</option>
                  <option value="non-veg">Non-Vegetarian</option>
                </select>
              </div>

              {/* Price Field */}
              <div style={{ marginBottom: '24px' }}>
                <label style={{
                  display: 'block',
                  fontSize: '14px',
                  fontWeight: '500',
                  color: '#374151',
                  marginBottom: '8px'
                }}>
                  Price *
                </label>
                <input
                  type="number"
                  step="0.01"
                  min="0"
                  value={formData.price}
                  onChange={(e) => setFormData({...formData, price: e.target.value})}
                  required
                  style={{
                    width: '100%',
                    padding: '12px 16px',
                    border: '2px solid #e5e7eb',
                    borderRadius: '8px',
                    fontSize: '14px',
                    outline: 'none',
                    transition: 'all 0.2s ease',
                    backgroundColor: 'white',
                    boxSizing: 'border-box'
                  }}
                  placeholder="Enter price (e.g., 12.99)"
                />
              </div>

              {/* Category Field */}
              <div style={{ marginBottom: '24px' }}>
                <label style={{
                  display: 'block',
                  fontSize: '14px',
                  fontWeight: '500',
                  color: '#374151',
                  marginBottom: '8px'
                }}>
                  Category
                </label>
                <input
                  type="text"
                  value={formData.category}
                  onChange={(e) => setFormData({...formData, category: e.target.value})}
                  style={{
                    width: '100%',
                    padding: '12px 16px',
                    border: '2px solid #e5e7eb',
                    borderRadius: '8px',
                    fontSize: '14px',
                    outline: 'none',
                    transition: 'all 0.2s ease',
                    backgroundColor: 'white',
                    boxSizing: 'border-box'
                  }}
                  placeholder="Enter category (e.g., Main Course, Appetizer)"
                />
              </div>

              {/* Image URL Field */}
              <div style={{ marginBottom: '24px' }}>
                <label style={{
                  display: 'block',
                  fontSize: '14px',
                  fontWeight: '500',
                  color: '#374151',
                  marginBottom: '8px'
                }}>
                  Image URL
                </label>
                <input
                  type="url"
                  value={formData.image}
                  onChange={(e) => setFormData({...formData, image: e.target.value})}
                  onFocus={() => setImageFocused(true)}
                  onBlur={() => setImageFocused(false)}
                  style={{
                    width: '100%',
                    padding: '12px 16px',
                    border: `2px solid ${imageFocused ? '#006D77' : '#e5e7eb'}`,
                    borderRadius: '8px',
                    fontSize: '14px',
                    outline: 'none',
                    transition: 'all 0.2s ease',
                    backgroundColor: imageFocused ? '#f0fdfa' : 'white',
                    boxSizing: 'border-box'
                  }}
                  placeholder="Enter image URL (optional)"
                />
              </div>

              {/* Mood Association Field */}
              <div style={{ marginBottom: '24px' }}>
                <label style={{
                  display: 'block',
                  fontSize: '14px',
                  fontWeight: '500',
                  color: '#374151',
                  marginBottom: '8px'
                }}>
                  Associated Mood *
                </label>
                <select
                  value={formData.mood_id}
                  onChange={(e) => setFormData({...formData, mood_id: e.target.value})}
                  required
                  style={{
                    width: '100%',
                    padding: '12px 16px',
                    border: '2px solid #e5e7eb',
                    borderRadius: '8px',
                    fontSize: '14px',
                    outline: 'none',
                    transition: 'all 0.2s ease',
                    backgroundColor: 'white',
                    boxSizing: 'border-box'
                  }}
                >
                  <option value="">Select a mood</option>
                  {moods.map(mood => (
                    <option key={mood.id} value={mood.id}>
                      {mood.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Form Message */}
              {formMessage && (
                <div style={{
                  padding: '12px 16px',
                  backgroundColor: formMessage.includes('success') ? '#d1fae5' : '#fee2e2',
                  color: formMessage.includes('success') ? '#065f46' : '#991b1b',
                  borderRadius: '8px',
                  marginBottom: '20px',
                  fontSize: '14px',
                  textAlign: 'center'
                }}>
                  {formMessage}
                </div>
              )}

              {/* Form Buttons */}
              <div style={{
                display: 'flex',
                gap: '12px',
                justifyContent: 'flex-end'
              }}>
                <button
                  type="button"
                  onClick={handleFormCancel}
                  onMouseEnter={() => setCancelHovered(true)}
                  onMouseLeave={() => setCancelHovered(false)}
                  style={{
                    padding: '12px 24px',
                    backgroundColor: cancelHovered ? '#f3f4f6' : 'white',
                    color: '#374151',
                    border: '2px solid #e5e7eb',
                    borderRadius: '8px',
                    fontSize: '14px',
                    fontWeight: '500',
                    cursor: 'pointer',
                    transition: 'all 0.2s ease'
                  }}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={formLoading}
                  onMouseEnter={() => setSubmitHovered(true)}
                  onMouseLeave={() => setSubmitHovered(false)}
                  style={{
                    padding: '12px 24px',
                    backgroundColor: formLoading ? '#9ca3af' : (submitHovered ? 'rgba(255, 111, 97, 0.9)' : '#FF6F61'),
                    color: 'white',
                    border: 'none',
                    borderRadius: '8px',
                    fontSize: '14px',
                    fontWeight: '500',
                    cursor: formLoading ? 'not-allowed' : 'pointer',
                    transition: 'all 0.2s ease',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px'
                  }}
                >
                  {formLoading && (
                    <div style={{
                      width: '16px',
                      height: '16px',
                      border: '2px solid transparent',
                      borderTop: '2px solid white',
                      borderRadius: '50%',
                      animation: 'spin 1s linear infinite'
                    }}></div>
                  )}
                  {formLoading ? 'Processing...' : (formMode === 'add' ? 'Add Food Item' : 'Update Food Item')}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
}

export default Menu;
