import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';

function DashboardSidebar() {
  const navigate = useNavigate();
  const location = useLocation();

  // Define sidebar navigation items
  const sidebarItems = [
    {
      id: 'dashboard',
      path: '/admin-dashboard',
      icon: 'fa-solid fa-home',
      label: 'Dashboard'
    },
    {
      id: 'books',
      path: '/admin/books',
      icon: 'fa-solid fa-book',
      label: 'Book Recommendations'
    },
    {
      id: 'moods',
      path: '/admin/moods',
      icon: 'fa-solid fa-heart',
      label: 'Mood Explorer'
    },
    {
      id: 'quizzes',
      path: '/admin/quizzes',
      icon: 'fa-solid fa-question-circle',
      label: 'Quiz Questions'
    },
    {
      id: 'menu',
      path: '/admin/menu-items',
      icon: 'fa-solid fa-utensils',
      label: 'Food Menu'
    },
    {
      id: 'journey',
      path: '#',
      icon: 'fa-solid fa-chart-line',
      label: 'My Journey'
    },
    {
      id: 'community',
      path: '#',
      icon: 'fa-solid fa-users',
      label: 'Community'
    },
    {
      id: 'settings',
      path: '#',
      icon: 'fa-solid fa-gear',
      label: 'Settings'
    }
  ];

  // Check if current path matches item path
  const isActive = (itemPath) => {
    return location.pathname === itemPath;
  };

  // Handle navigation
  const handleNavigation = (path) => {
    if (path !== '#') {
      navigate(path);
    }
  };

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      bottom: 0,
      width: '256px',
      backgroundColor: 'white',
      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
      zIndex: 40,
      paddingTop: '70px',
      display: 'flex',
      flexDirection: 'column'
    }}>
      <div style={{ flex: 1, overflowY: 'auto', padding: '16px' }}>
        <nav style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
          {sidebarItems.map((item) => (
            <span
              key={item.id}
              style={{
                display: 'flex',
                alignItems: 'center',
                padding: '12px 16px',
                backgroundColor: isActive(item.path) ? 'rgba(255, 111, 97, 0.1)' : 'transparent',
                color: isActive(item.path) ? '#FF6F61' : '#374151',
                borderRadius: '8px',
                cursor: item.path !== '#' ? 'pointer' : 'default',
                fontWeight: isActive(item.path) ? '500' : 'normal'
              }}
              className={item.path !== '#' ? 'hover:bg-gray-100' : ''}
              onClick={() => handleNavigation(item.path)}
            >
              <i className={item.icon} style={{ width: '20px', textAlign: 'center' }}></i>
              <span style={{ marginLeft: '12px' }}>{item.label}</span>
            </span>
          ))}
        </nav>
      </div>

      {/* Bottom Graphics - Mood Streak Card */}
      <div style={{ padding: '16px', borderTop: '1px solid #e5e7eb' }}>
        <div style={{
          background: 'linear-gradient(to right, #006D77, rgba(255, 111, 97, 0.6))',
          borderRadius: '12px',
          padding: '16px',
          color: 'white',
          marginBottom: '16px'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>
            <i className="fa-solid fa-fire-flame-curved" style={{ fontSize: '20px' }}></i>
            <span style={{ marginLeft: '8px', fontWeight: '600' }}>Mood Streak</span>
          </div>
          <div style={{ fontSize: '24px', fontWeight: 'bold', marginBottom: '4px' }}>12 Days</div>
          <div style={{ fontSize: '12px', opacity: 0.8 }}>Keep tracking your moods to grow your streak!</div>
        </div>

        <div style={{ fontSize: '12px', color: '#6b7280', textAlign: 'center' }}>
          <div>Need help? <span style={{ color: '#006D77', cursor: 'pointer' }}>Contact support</span></div>
          <div style={{ marginTop: '4px' }}>LiberateBites v2.1.0</div>
        </div>
      </div>
    </div>
  );
}

export default DashboardSidebar;
