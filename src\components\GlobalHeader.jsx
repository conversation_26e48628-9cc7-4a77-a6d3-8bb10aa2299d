import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

function GlobalHeader() {
  const navigate = useNavigate();
  const [showMobileMenu, setShowMobileMenu] = useState(false);
  const [showDropdown, setShowDropdown] = useState(false);
  const [userName, setUserName] = useState('User');
  const [userRole, setUserRole] = useState('user');

  useEffect(() => {
    // Get user data from localStorage
    const storedUser = localStorage.getItem('user');
    if (storedUser) {
      try {
        const userData = JSON.parse(storedUser);
        if (userData && userData.name) {
          setUserName(userData.name);
        }
        if (userData && userData.role) {
          setUserRole(userData.role);
        }
      } catch (error) {
        console.error('Error parsing user data from localStorage:', error);
      }
    }
  }, []);

  const toggleMobileMenu = () => {
    setShowMobileMenu(!showMobileMenu);
  };

  const handleNavigation = (path) => {
    navigate(path);
    setShowMobileMenu(false); // Close mobile menu after navigation
  };

  const handleLogout = async () => {
    try {
      const authToken = localStorage.getItem('authToken');

      // Call logout API endpoint
      await fetch('http://168.231.122.170/cstm-api/api/admin/logout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authToken}`
        }
      });
    } catch (error) {
      console.error('Logout API error:', error);
    } finally {
      // Always clear local storage and redirect, even if API call fails
      localStorage.removeItem('authToken');
      localStorage.removeItem('user');
      navigate('/login');
    }
  };

  const styles = {
    header: {
      fontFamily: 'Inter, sans-serif',
      height: '70px',
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      zIndex: 50,
      transition: 'all 0.3s ease-in-out',
      boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
      background: 'linear-gradient(to right, #F5E8C7, #F7EFD6, #F5E8C7)'
    },
    container: {
      maxWidth: '1200px',
      margin: '0 auto',
      padding: '0 16px',
      height: '100%'
    },
    flexBetween: {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
      height: '100%'
    },
    flexCenter: {
      display: 'flex',
      alignItems: 'center'
    },
    logo: {
      backgroundColor: '#FF6F61',
      width: '40px',
      height: '40px',
      borderRadius: '50%',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
      cursor: 'pointer'
    },
    logoText: {
      color: 'white',
      fontWeight: 'bold',
      fontSize: '18px'
    },
    brandName: {
      marginLeft: '12px',
      color: '#006D77',
      fontWeight: '600',
      fontSize: '20px',
      cursor: 'pointer'
    },
    desktopNav: {
      display: 'none',
      alignItems: 'center',
      gap: '32px'
    },
    navLink: {
      color: '#006D77',
      fontWeight: '500',
      cursor: 'pointer',
      transition: 'color 0.3s ease',
      position: 'relative',
      padding: '8px 0'
    },
    userSection: {
      display: 'flex',
      alignItems: 'center',
      gap: '16px'
    },
    settingsBtn: {
      color: '#006D77',
      padding: '8px',
      borderRadius: '50%',
      border: 'none',
      background: 'transparent',
      cursor: 'pointer',
      transition: 'background-color 0.3s ease',
      position: 'relative'
    },
    dropdownContainer: {
      position: 'relative',
      display: 'inline-block'
    },
    dropdown: {
      position: 'absolute',
      top: '100%',
      right: 0,
      backgroundColor: 'white',
      borderRadius: '12px',
      boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
      border: '1px solid #e5e7eb',
      minWidth: '180px',
      zIndex: 1000,
      marginTop: '8px',
      opacity: showDropdown ? 1 : 0,
      visibility: showDropdown ? 'visible' : 'hidden',
      transform: showDropdown ? 'translateY(0)' : 'translateY(-10px)',
      transition: 'all 0.2s ease-in-out'
    },
    dropdownItem: {
      display: 'flex',
      alignItems: 'center',
      padding: '12px 16px',
      color: '#374151',
      cursor: 'pointer',
      transition: 'background-color 0.2s ease',
      fontSize: '14px',
      fontWeight: '500'
    },
    dropdownItemFirst: {
      borderTopLeftRadius: '12px',
      borderTopRightRadius: '12px'
    },
    dropdownItemLast: {
      borderBottomLeftRadius: '12px',
      borderBottomRightRadius: '12px',
      color: '#dc2626'
    },
    dropdownIcon: {
      marginRight: '12px',
      fontSize: '16px'
    },
    userInfo: {
      marginRight: '12px',
      textAlign: 'right',
      display: 'none'
    },
    welcomeText: {
      color: '#006D77',
      fontSize: '14px',
      fontWeight: '500',
      margin: 0
    },
    userNameText: {
      color: '#FF6F61',
      fontSize: '12px',
      margin: 0
    },
    avatar: {
      width: '40px',
      height: '40px',
      borderRadius: '50%',
      overflow: 'hidden',
      border: '2px solid #FF6F61',
      cursor: 'pointer'
    },
    avatarImg: {
      width: '100%',
      height: '100%',
      objectFit: 'cover'
    },
    mobileMenuBtn: {
      display: 'block',
      color: '#006D77',
      padding: '8px',
      borderRadius: '50%',
      border: 'none',
      background: 'transparent',
      cursor: 'pointer',
      transition: 'background-color 0.3s ease'
    },
    mobileNav: {
      display: showMobileMenu ? 'block' : 'none',
      backgroundColor: '#F5E8C7',
      position: 'absolute',
      top: '60px',
      left: 0,
      right: 0,
      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
    },
    mobileNavContainer: {
      maxWidth: '1200px',
      margin: '0 auto',
      padding: '16px'
    },
    mobileNavList: {
      display: 'flex',
      flexDirection: 'column',
      gap: '16px'
    },
    mobileNavLink: {
      color: '#006D77',
      fontWeight: '500',
      padding: '8px 16px',
      borderRadius: '8px',
      cursor: 'pointer',
      transition: 'background-color 0.3s ease'
    },
    mobileUserSection: {
      display: 'flex',
      alignItems: 'center',
      padding: '8px 16px'
    },
    mobileUserInfo: {
      marginLeft: '12px'
    },
    divider: {
      border: 'none',
      borderTop: '1px solid #EAD9B7',
      margin: '8px 0'
    },
    logoutLink: {
      display: 'flex',
      alignItems: 'center',
      color: '#dc2626',
      fontWeight: '500',
      padding: '8px 16px',
      borderRadius: '8px',
      cursor: 'pointer',
      transition: 'background-color 0.3s ease'
    }
  };

  return (
    <>
      <style>
        {`
          .nav-link-hover:hover {
            color: #FF6F61 !important;
          }

          .nav-link-hover::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 0;
            height: 2px;
            background-color: #FF6F61;
            transition: width 0.3s ease;
          }

          .nav-link-hover:hover::after {
            width: 100%;
          }

          .mobile-nav-hover:hover {
            background-color: #EAD9B7 !important;
          }

          .settings-hover:hover {
            background-color: #EAD9B7 !important;
          }

          .logout-hover:hover {
            background-color: #fef2f2 !important;
          }

          .dropdown-item-hover:hover {
            background-color: #f9fafb !important;
          }

          .dropdown-logout-hover:hover {
            background-color: #fef2f2 !important;
          }

          @media (min-width: 768px) {
            .desktop-nav {
              display: flex !important;
            }
            .mobile-menu-btn {
              display: none !important;
            }
            .user-info-desktop {
              display: block !important;
            }
            .brand-name-desktop {
              display: inline !important;
            }
          }

          @media (max-width: 767px) {
            .brand-name-desktop {
              display: none !important;
            }
          }
        `}
      </style>

      <header style={styles.header}>
        <div style={styles.container}>
          <div style={styles.flexBetween}>
            {/* Logo Section */}
            <div style={styles.flexCenter}>
              <div style={styles.flexCenter} onClick={() => handleNavigation('/main-menu')}>
                <div style={styles.logo}>
                  <span style={styles.logoText}>LB</span>
                </div>
                <span style={styles.brandName} className="brand-name-desktop">LiberateBites</span>
              </div>
            </div>

            {/* Desktop Navigation */}
            <nav style={styles.desktopNav} className="desktop-nav">
              <span
                style={styles.navLink}
                className="nav-link-hover"
                onClick={() => handleNavigation('/main-menu')}
              >
                Home
              </span>
              <span
                style={styles.navLink}
                className="nav-link-hover"
                onClick={() => handleNavigation('/food-menu')}
              >
                Menu
              </span>
              {userRole === 'admin' && (
                <>
                  <span
                    style={styles.navLink}
                    className="nav-link-hover"
                    onClick={() => handleNavigation('/admin/moods')}
                  >
                    Moods
                  </span>
                  <span
                    style={styles.navLink}
                    className="nav-link-hover"
                    onClick={() => handleNavigation('/admin/books')}
                  >
                    Books
                  </span>
                </>
              )}
            </nav>

            {/* User Profile & Settings */}
            <div style={styles.userSection}>
              <div style={styles.flexCenter} className="desktop-nav">
                <div
                  style={styles.dropdownContainer}
                  onMouseEnter={() => setShowDropdown(true)}
                  onMouseLeave={() => setShowDropdown(false)}
                >
                  <button style={styles.settingsBtn} className="settings-hover">
                    <i className="fa-solid fa-sliders" style={{fontSize: '18px'}}></i>
                  </button>

                  {/* Dropdown Menu */}
                  <div style={styles.dropdown}>
                    <div
                      style={{...styles.dropdownItem, ...styles.dropdownItemFirst}}
                      className="dropdown-item-hover"
                    >
                      <i className="fa-solid fa-user" style={styles.dropdownIcon}></i>
                      Profile
                    </div>
                    <div
                      style={styles.dropdownItem}
                      className="dropdown-item-hover"
                      onClick={() => handleNavigation(userRole === 'admin' ? '/admin-dashboard' : '/user-dashboard')}
                    >
                      <i className="fa-solid fa-chart-line" style={styles.dropdownIcon}></i>
                      Dashboard
                    </div>
                    {userRole === 'admin' && (
                      <div
                        style={styles.dropdownItem}
                        className="dropdown-item-hover"
                        onClick={() => handleNavigation('/admin/menu-items')}
                      >
                        <i className="fa-solid fa-utensils" style={styles.dropdownIcon}></i>
                        Food Menu
                      </div>
                    )}
                    <div
                      style={{...styles.dropdownItem, ...styles.dropdownItemLast}}
                      className="dropdown-logout-hover"
                      onClick={handleLogout}
                    >
                      <i className="fa-solid fa-sign-out-alt" style={styles.dropdownIcon}></i>
                      Logout
                    </div>
                  </div>
                </div>

                <div style={{...styles.flexCenter, marginLeft: '16px'}}>
                  <div style={styles.userInfo} className="user-info-desktop">
                    <p style={styles.welcomeText}>Welcome back</p>
                    <p style={styles.userNameText}>{userName} ({userRole === 'admin' ? 'Admin' : 'User'})</p>
                  </div>
                  <div style={styles.avatar}>
                    <img
                      src="https://storage.googleapis.com/uxpilot-auth.appspot.com/avatars/avatar-2.jpg"
                      alt="User Avatar"
                      style={styles.avatarImg}
                    />
                  </div>
                </div>
              </div>

              {/* Mobile Menu Button */}
              <button
                style={styles.mobileMenuBtn}
                className="mobile-menu-btn settings-hover"
                onClick={toggleMobileMenu}
              >
                <i className={`fa-solid ${showMobileMenu ? 'fa-xmark' : 'fa-bars'}`} style={{fontSize: '20px'}}></i>
              </button>
            </div>
          </div>
        </div>

        {/* Mobile Navigation Dropdown */}
        <div style={styles.mobileNav}>
          <div style={styles.mobileNavContainer}>
            <nav style={styles.mobileNavList}>
              <span
                style={styles.mobileNavLink}
                className="mobile-nav-hover"
                onClick={() => handleNavigation('/main-menu')}
              >
                Home
              </span>
              <span
                style={styles.mobileNavLink}
                className="mobile-nav-hover"
                onClick={() => handleNavigation('/food-menu')}
              >
                Menu
              </span>
              {userRole === 'admin' && (
                <>
                  <span
                    style={styles.mobileNavLink}
                    className="mobile-nav-hover"
                    onClick={() => handleNavigation('/admin/moods')}
                  >
                    Moods
                  </span>
                  <span
                    style={styles.mobileNavLink}
                    className="mobile-nav-hover"
                    onClick={() => handleNavigation('/admin/books')}
                  >
                    Books
                  </span>
                </>
              )}
              <hr style={styles.divider} />
              <div style={styles.mobileUserSection}>
                <div style={styles.avatar}>
                  <img
                    src="https://storage.googleapis.com/uxpilot-auth.appspot.com/avatars/avatar-2.jpg"
                    alt="User Avatar"
                    style={styles.avatarImg}
                  />
                </div>
                <div style={styles.mobileUserInfo}>
                  <p style={styles.welcomeText}>{userName}</p>
                  <p style={styles.userNameText}>{userRole === 'admin' ? 'Administrator' : 'User'}</p>
                </div>
              </div>
              <span
                style={{...styles.mobileNavLink, display: 'flex', alignItems: 'center'}}
                className="mobile-nav-hover"
              >
                <i className="fa-solid fa-sliders" style={{marginRight: '8px'}}></i> Settings
              </span>
              <span
                style={styles.logoutLink}
                className="logout-hover"
                onClick={handleLogout}
              >
                <i className="fa-solid fa-sign-out-alt" style={{marginRight: '8px'}}></i> Logout
              </span>
            </nav>
          </div>
        </div>
      </header>
    </>
  );
}

export default GlobalHeader;
