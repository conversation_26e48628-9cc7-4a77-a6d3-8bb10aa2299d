import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import logo from '../assets/logo.svg';

const styles = {
  container: {
    minHeight: '100vh',
    background: '#F5E8C7', // AdminDashboard cream background
    fontFamily: 'Inter, sans-serif',
    position: 'relative',
    overflow: 'hidden'
  },
  // Animated background elements
  animatedBg: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 0,
    overflow: 'hidden'
  },
  bokeh: {
    position: 'absolute',
    borderRadius: '50%',
    background: 'radial-gradient(circle, rgba(255, 111, 97, 0.3) 0%, rgba(255, 111, 97, 0) 70%)',
    opacity: 0.3,
    filter: 'blur(3px)',
    pointerEvents: 'none',
    animation: 'bokehFade 8s ease-in-out infinite'
  },
  lightRay: {
    position: 'absolute',
    background: 'linear-gradient(45deg, rgba(255, 111, 97, 0.05) 0%, rgba(255, 218, 185, 0.1) 100%)',
    transform: 'rotate(45deg)',
    transformOrigin: 'center',
    pointerEvents: 'none',
    animation: 'pulseLight 6s ease-in-out infinite'
  },
  gradientBg: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: 'linear-gradient(-45deg, #F5E8C7, #FFDAB9, #F5E8C7, #FFE2C7)',
    backgroundSize: '400% 400%',
    animation: 'gradientShift 10s ease infinite',
    zIndex: 0
  },
  loginContainer: {
    minHeight: '100vh',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    overflow: 'hidden'
  },
  backgroundContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1
  },
  backgroundImage: {
    width: '100%',
    height: '100%',
    objectFit: 'cover',
    opacity: 0.2
  },
  backgroundOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: 'linear-gradient(135deg, rgba(0, 109, 119, 0.1) 0%, rgba(255, 111, 97, 0.05) 50%, rgba(131, 197, 190, 0.1) 100%)' // AdminDashboard colors
  },
  loginCard: {
    position: 'relative',
    zIndex: 10,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    backdropFilter: 'blur(10px)',
    borderRadius: '24px',
    boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
    padding: '48px',
    width: '100%',
    maxWidth: '448px',
    margin: '32px 16px',
    border: '1px solid rgba(255, 255, 255, 0.2)'
  },
  logoSection: {
    textAlign: 'center',
    marginBottom: '40px'
  },
  logoContainer: {
    display: 'inline-flex',
    alignItems: 'center',
    justifyContent: 'center',
    width: '80px',
    height: '80px',
    borderRadius: '16px',
    marginBottom: '24px',
    boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)'
  },
  logoImage: {
    width: '80px',
    height: '80px',
    objectFit: 'contain'
  },
  logoIcon: {
    color: 'white',
    fontSize: '24px'
  },
  title: {
    fontSize: '30px',
    fontWeight: 'bold',
    color: '#006D77', // AdminDashboard teal color
    marginBottom: '8px'
  },
  form: {
    display: 'flex',
    flexDirection: 'column',
    gap: '24px'
  },
  fieldContainer: {
    display: 'flex',
    flexDirection: 'column',
    gap: '8px'
  },
  label: {
    display: 'block',
    fontSize: '14px',
    fontWeight: '600',
    color: '#006D77' // AdminDashboard teal color
  },
  inputContainer: {
    position: 'relative',
    transition: 'transform 0.2s ease'
  },
  inputContainerHover: {
    transform: 'scale(1.02)'
  },
  input: {
    width: '100%',
    padding: '16px',
    backgroundColor: '#f9fafb',
    border: '1px solid rgba(0, 109, 119, 0.3)', // AdminDashboard teal border
    borderRadius: '12px',
    outline: 'none',
    transition: 'all 0.2s ease',
    color: '#111827',
    fontSize: '16px',
    boxSizing: 'border-box'
  },
  inputFocus: {
    boxShadow: '0 0 0 3px rgba(255, 111, 97, 0.2)', // AdminDashboard coral focus
    borderColor: '#FF6F61' // AdminDashboard coral color
  },
  inputIcon: {
    position: 'absolute',
    top: '50%',
    right: '16px',
    transform: 'translateY(-50%)',
    color: 'rgba(0, 109, 119, 0.6)', // AdminDashboard teal icon
    fontSize: '14px'
  },
  loginButtonContainer: {
    paddingTop: '8px'
  },
  loginButton: {
    width: '100%',
    backgroundColor: '#FF6F61', // AdminDashboard coral color
    color: 'white',
    fontWeight: 'bold',
    padding: '16px 24px',
    borderRadius: '12px',
    border: 'none',
    cursor: 'pointer',
    boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
    transition: 'all 0.2s ease',
    fontSize: '16px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    gap: '8px'
  },
  loginButtonHover: {
    backgroundColor: 'rgba(255, 111, 97, 0.9)', // AdminDashboard coral hover
    boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1)',
    transform: 'scale(1.02)'
  },
  loginButtonDisabled: {
    opacity: 0.5,
    cursor: 'not-allowed'
  },
  messageContainer: {
    textAlign: 'center',
    paddingTop: '8px'
  },
  successMessage: {
    color: '#059669',
    fontWeight: '500',
    fontSize: '14px'
  },
  errorMessage: {
    color: '#dc2626',
    fontWeight: '500',
    fontSize: '14px'
  },
  forgotPassword: {
    textAlign: 'center',
    paddingTop: '16px'
  },
  forgotPasswordLink: {
    fontSize: '14px',
    color: '#4b5563',
    fontWeight: '500',
    cursor: 'pointer',
    transition: 'color 0.2s ease'
  },
  forgotPasswordLinkHover: {
    color: '#FF6F61' // AdminDashboard coral color
  },
  divider: {
    display: 'flex',
    alignItems: 'center',
    margin: '32px 0'
  },
  dividerLine: {
    flex: 1,
    height: '1px',
    backgroundColor: '#e5e7eb'
  },
  dividerText: {
    padding: '0 16px',
    fontSize: '14px',
    color: '#6b7280',
    fontWeight: '500'
  },
  socialLogin: {
    display: 'flex',
    flexDirection: 'column',
    gap: '12px'
  },
  socialButton: {
    width: '100%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    gap: '12px',
    padding: '12px 16px',
    border: '1px solid #e5e7eb',
    borderRadius: '12px',
    backgroundColor: 'white',
    cursor: 'pointer',
    transition: 'background-color 0.2s ease',
    fontSize: '16px'
  },
  socialButtonHover: {
    backgroundColor: '#f9fafb'
  },
  socialButtonText: {
    color: '#374151',
    fontWeight: '500'
  },
  signupSection: {
    textAlign: 'center',
    marginTop: '24px',
    paddingTop: '24px',
    borderTop: '1px solid #f3f4f6'
  },
  signupText: {
    color: '#4b5563'
  },
  signupLink: {
    color: '#FF6F61', // AdminDashboard coral color
    fontWeight: '600',
    cursor: 'pointer',
    transition: 'color 0.2s ease',
    marginLeft: '4px'
  },
  signupLinkHover: {
    color: '#006D77' // AdminDashboard teal color
  }
};

// CSS animations for the background effects
const cssAnimations = `
  @keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
  }

  @keyframes pulseLight {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 0.7; }
  }

  @keyframes bokehFade {
    0%, 100% { opacity: 0.2; }
    50% { opacity: 0.6; }
  }

  @keyframes float {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-20px); }
  }
`;

function Login() {
  const navigate = useNavigate();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState('');

  // Check if user is already logged in
  useEffect(() => {
    const authToken = localStorage.getItem('authToken');
    if (authToken) {
      // User is already logged in, redirect to main menu
      navigate('/main-menu');
    }
  }, [navigate]);
  const [emailFocused, setEmailFocused] = useState(false);
  const [passwordFocused, setPasswordFocused] = useState(false);
  const [emailHovered, setEmailHovered] = useState(false);
  const [passwordHovered, setPasswordHovered] = useState(false);
  const [buttonHovered, setButtonHovered] = useState(false);
  const [forgotPasswordHovered, setForgotPasswordHovered] = useState(false);
  const [signupHovered, setSignupHovered] = useState(false);


  const handleLogin = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    setMessage('');

    try {
      console.log('Attempting login with:', { email, password: '***' });

      const response = await fetch('http://168.231.122.170/cstm-api/api/admin/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: email,
          password: password
        })
      });

      console.log('Response status:', response.status);
      const data = await response.json();
      console.log('Response data:', data);

      if (response.ok && data.token) {
        localStorage.setItem('authToken', data.token);
        localStorage.setItem('user', JSON.stringify(data.user));

        setMessage('User logged in');

        setTimeout(() => {
          navigate('/main-menu');
        }, 1000);
      } else {
        setMessage('Unable to login');
      }
    } catch (error) {
      console.error('Login error:', error);
      setMessage('Unable to login');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div style={styles.container}>
      {/* Add CSS animations */}
      <style>{cssAnimations}</style>

      {/* Animated background */}
      <div style={styles.gradientBg}></div>
      <div style={styles.animatedBg}>
        {/* Bokeh effects */}
        <div style={{
          ...styles.bokeh,
          width: '128px',
          height: '128px',
          top: '40px',
          left: '80px'
        }}></div>
        <div style={{
          ...styles.bokeh,
          width: '96px',
          height: '96px',
          top: '25%',
          right: '33%',
          animationDelay: '2s'
        }}></div>
        <div style={{
          ...styles.bokeh,
          width: '160px',
          height: '160px',
          bottom: '25%',
          left: '33%',
          animationDelay: '4s'
        }}></div>
        <div style={{
          ...styles.bokeh,
          width: '80px',
          height: '80px',
          bottom: '40px',
          right: '80px',
          animationDelay: '3s'
        }}></div>

        {/* Light rays */}
        <div style={{
          ...styles.lightRay,
          width: '800px',
          height: '128px',
          left: '-200px',
          top: '33%'
        }}></div>
        <div style={{
          ...styles.lightRay,
          width: '600px',
          height: '96px',
          right: '-100px',
          top: '66%',
          animationDelay: '3s'
        }}></div>
      </div>

      <div style={styles.loginContainer}>
        {/* Background Image and Overlay */}
        <div style={styles.backgroundContainer}>
          <img
            style={styles.backgroundImage}
            src="https://storage.googleapis.com/uxpilot-auth.appspot.com/8e688dbc78-e30af8ef0257480e8948.png"
            alt="cozy dining table with warm lighting, books and coffee, soft blur background"
          />
          <div style={styles.backgroundOverlay}></div>
        </div>

        {/* Login Card */}
        <div style={styles.loginCard}>
          {/* Logo Section */}
          <div style={styles.logoSection}>
            <div style={styles.logoContainer}>
              <img
                src={logo}
                alt="Liberate Bites Logo"
                style={styles.logoImage}
              />
            </div>
            <h1 style={styles.title}>
              Liberate Bites
            </h1>
          </div>

          {/* Login Form */}
          <form onSubmit={handleLogin} style={styles.form}>
            {/* Email Field */}
            <div style={styles.fieldContainer}>
              <label htmlFor="email" style={styles.label}>
                Email Address
              </label>
              <div 
                style={{
                  ...styles.inputContainer,
                  ...(emailHovered ? styles.inputContainerHover : {})
                }}
                onMouseEnter={() => setEmailHovered(true)}
                onMouseLeave={() => setEmailHovered(false)}
              >
                <input 
                  type="email" 
                  id="email" 
                  name="email" 
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  onFocus={() => setEmailFocused(true)}
                  onBlur={() => setEmailFocused(false)}
                  style={{
                    ...styles.input,
                    ...(emailFocused ? styles.inputFocus : {})
                  }}
                  placeholder="Enter your email" 
                  required
                />
                <div style={styles.inputIcon}>
                  <i className="fas fa-envelope"></i>
                </div>
              </div>
            </div>

            {/* Password Field */}
            <div style={styles.fieldContainer}>
              <label htmlFor="password" style={styles.label}>
                Password
              </label>
              <div 
                style={{
                  ...styles.inputContainer,
                  ...(passwordHovered ? styles.inputContainerHover : {})
                }}
                onMouseEnter={() => setPasswordHovered(true)}
                onMouseLeave={() => setPasswordHovered(false)}
              >
                <input 
                  type="password" 
                  id="password" 
                  name="password" 
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  onFocus={() => setPasswordFocused(true)}
                  onBlur={() => setPasswordFocused(false)}
                  style={{
                    ...styles.input,
                    ...(passwordFocused ? styles.inputFocus : {})
                  }}
                  placeholder="Enter your password" 
                  required
                />
                <div style={styles.inputIcon}>
                  <i className="fas fa-lock"></i>
                </div>
              </div>
            </div>

            {/* Login Button */}
            <div style={styles.loginButtonContainer}>
              <button
                type="submit"
                disabled={isLoading}
                style={{
                  ...styles.loginButton,
                  ...(buttonHovered && !isLoading ? styles.loginButtonHover : {}),
                  ...(isLoading ? styles.loginButtonDisabled : {})
                }}
                onMouseEnter={() => setButtonHovered(true)}
                onMouseLeave={() => setButtonHovered(false)}
              >
                {isLoading ? (
                  <>
                    <i className="fas fa-spinner fa-spin"></i>
                    Logging in...
                  </>
                ) : (
                  <>
                    <span>Login</span>
                    <i className="fas fa-arrow-right"></i>
                  </>
                )}
              </button>
            </div>

            {/* Message Display */}
            {message && (
              <div style={styles.messageContainer}>
                <div style={message === 'User logged in' ? styles.successMessage : styles.errorMessage}>
                  {message}
                </div>
              </div>
            )}

            {/* Forgot Password */}
            <div style={styles.forgotPassword}>
              <span
                style={{
                  ...styles.forgotPasswordLink,
                  ...(forgotPasswordHovered ? styles.forgotPasswordLinkHover : {})
                }}
                onMouseEnter={() => setForgotPasswordHovered(true)}
                onMouseLeave={() => setForgotPasswordHovered(false)}
              >
                Forgot Password?
              </span>
            </div>
          </form>

          {/* Sign Up Link */}
          <div style={styles.signupSection}>
            <p style={styles.signupText}>
              Don't have an account?
              <span
                style={{
                  ...styles.signupLink,
                  ...(signupHovered ? styles.signupLinkHover : {})
                }}
                onMouseEnter={() => setSignupHovered(true)}
                onMouseLeave={() => setSignupHovered(false)}
                onClick={() => navigate('/register')}
              >
                Sign up
              </span>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Login;