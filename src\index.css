@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles for Liberal Bites */
::-webkit-scrollbar {
  display: none;
}

html, body {
  -ms-overflow-style: none;
  scrollbar-width: none;
  font-family: 'Inter', sans-serif;
}

body {
  margin: 0;
}

/* Floating animation for quiz page */
@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* Masonry grid for food menu */
.masonry-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  grid-gap: 24px;
}

@media (min-width: 768px) {
  .masonry-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .masonry-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

.food-card {
  transition: transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out;
}

.food-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Additional animations for main menu */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes pulse-slow {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

/* Custom color utilities */
@layer utilities {
  .bg-peach {
    background-color: #FFEAA7;
  }
  .bg-orange {
    background-color: #E17055;
  }
  .bg-red-accent {
    background-color: #D63031;
  }
  .text-red-accent {
    color: #D63031;
  }
  .border-orange {
    border-color: #E17055;
  }
  .from-peach {
    --tw-gradient-from: #FFEAA7 var(--tw-gradient-from-position);
    --tw-gradient-to: rgb(255 234 167 / 0) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
  }
  .from-orange {
    --tw-gradient-from: #E17055 var(--tw-gradient-from-position);
    --tw-gradient-to: rgb(225 112 85 / 0) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
  }
  .to-red-accent {
    --tw-gradient-to: #D63031 var(--tw-gradient-to-position);
  }
  .to-orange-100 {
    --tw-gradient-to: #fed7aa var(--tw-gradient-to-position);
  }
}
