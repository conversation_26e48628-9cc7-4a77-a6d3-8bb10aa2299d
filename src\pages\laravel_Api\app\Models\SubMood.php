<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class SubMood extends Model
{
    public function up()
    {
        Schema::create('sub_moods', function (Blueprint $table) {
            $table->id();
            $table->foreignId('mood_id')->constrained()->onDelete('cascade');
            $table->string('name');
            $table->text('meaning')->nullable();
            $table->timestamps();
        });
    }
    protected $fillable = ['mood_id', 'name', 'meaning'];

    public function mood()
    {
        return $this->belongsTo(Mood::class);
    }
    public function books()
    {
        return $this->belongsToMany(Book::class, 'book_sub_mood');
    }


}
