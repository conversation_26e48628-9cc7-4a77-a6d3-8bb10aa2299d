import React from 'react';
import { Outlet, useLocation } from 'react-router-dom';
import GlobalHeader from './GlobalHeader';
import GlobalFooter from './GlobalFooter';

function DashboardLayout() {
  const location = useLocation();

  // Check if we're on any dashboard page (should not have footer)
  const isDashboardPage = location.pathname === '/admin-dashboard' ||
                          location.pathname === '/user-dashboard' ||
                          location.pathname === '/admin/books' ||
                          location.pathname === '/admin/moods' ||
                          location.pathname === '/admin/quizzes' ||
                          location.pathname === '/foods' ||
                          location.pathname === '/admin/menu-items';

  return (
    <>
      <GlobalHeader />
      <div style={{ paddingTop: '70px', minHeight: 'calc(100vh - 70px)', display: 'flex', flexDirection: 'column' }}>
        <div style={{ flex: 1 }}>
          <Outlet />
        </div>
        {!isDashboardPage && <GlobalFooter />}
      </div>
    </>
  );
}

export default DashboardLayout;
