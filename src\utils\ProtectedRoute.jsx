import React from 'react';
import { Navigate } from 'react-router-dom';
import AccessDenied from '../components/AccessDenied';

const ProtectedRoute = ({ children, requireAdmin = false }) => {
  const isAuthenticated = !!localStorage.getItem('authToken'); // Check if token exists

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  // Check admin role if required
  if (requireAdmin) {
    const storedUser = localStorage.getItem('user');
    if (storedUser) {
      try {
        const userData = JSON.parse(storedUser);
        if (!userData || userData.role !== 'admin') {
          // User is authenticated but not admin, show access denied
          return <AccessDenied />;
        }
      } catch (error) {
        console.error('Error parsing user data from localStorage:', error);
        // If we can't parse user data, redirect to login
        return <Navigate to="/login" replace />;
      }
    } else {
      // No user data found, redirect to login
      return <Navigate to="/login" replace />;
    }
  }

  return children;
};

export default ProtectedRoute;
