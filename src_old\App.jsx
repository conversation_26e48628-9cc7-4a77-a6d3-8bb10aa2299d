import { Routes, Route, Navigate } from 'react-router-dom'
import Login from './pages/Login'
import Moods from './pages/Moods'
import Books from './pages/Books'
import Foods from './pages/Foods'
import DashboardLayout from './components/DashboardLayout'
import ProtectedRoute from './utils/ProtectedRoute'

function App() {
  return (
    <Routes>
      <Route path="/login" element={<Login />} />
      <Route element={<ProtectedRoute><DashboardLayout /></ProtectedRoute>}>
        <Route path="/moods" element={<Moods />} />
        <Route path="/books" element={<Books />} />
        <Route path="/foods" element={<Foods />} />
      </Route>
      <Route path="*" element={<Navigate to="/login" />} />
    </Routes>
  )
}

export default App
