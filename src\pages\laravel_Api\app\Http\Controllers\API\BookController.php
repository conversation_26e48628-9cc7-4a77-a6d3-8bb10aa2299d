<?php
namespace App\Http\Controllers\API;

use App\Models\Book;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class BookController extends Controller
{
    public function index()
    {
        return response()->json(Book::with('subMoods')->get());
    }

    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string',
            'author' => 'required|string',
            'description' => 'nullable|string',
            'sub_moods' => 'array' // array of sub_mood IDs
        ]);

        $book = Book::create($request->only(['title', 'author', 'description']));

        if ($request->has('sub_moods')) {
            $book->subMoods()->sync($request->sub_moods);
        }

        return response()->json($book->load('subMoods'), 201);
    }

    public function show($id)
    {
        return response()->json(Book::with('subMoods')->findOrFail($id));
    }

    public function update(Request $request, $id)
    {
        $book = Book::findOrFail($id);

        $book->update($request->only(['title', 'author', 'description']));

        if ($request->has('sub_moods')) {
            $book->subMoods()->sync($request->sub_moods);
        }

        return response()->json($book->load('subMoods'));
    }

    public function destroy($id)
    {
        Book::findOrFail($id)->delete();
        return response()->json(['message' => 'Book deleted']);
    }
}
