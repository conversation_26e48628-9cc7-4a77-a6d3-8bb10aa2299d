import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import GlobalHeader from '../components/GlobalHeader';
import GlobalFooter from '../components/GlobalFooter';

function MainMenu() {
  const navigate = useNavigate();
  const [userName, setUserName] = useState('User');
  const [quizMessage, setQuizMessage] = useState(null);
  const [showMessage, setShowMessage] = useState(false);

  useEffect(() => {
    // Get user data from localStorage
    const storedUser = localStorage.getItem('user');
    if (storedUser) {
      try {
        const userData = JSON.parse(storedUser);
        if (userData && userData.name) {
          setUserName(userData.name);
        }
      } catch (error) {
        console.error('Error parsing user data from localStorage:', error);
      }
    }

    // Check for quiz completion message
    const quizMessageData = localStorage.getItem('quizMessage');
    if (quizMessageData) {
      try {
        const messageObj = JSON.parse(quizMessageData);
        setQuizMessage(messageObj);
        setShowMessage(true);

        // Remove the message from localStorage so it doesn't show again
        localStorage.removeItem('quizMessage');

        // Auto-hide message after 8 seconds
        setTimeout(() => {
          setShowMessage(false);
        }, 8000);
      } catch (error) {
        console.error('Error parsing quiz message:', error);
        localStorage.removeItem('quizMessage');
      }
    }
  }, []);

  const handleStartQuiz = () => {
    navigate('/quiz');
  };

  const handleExploreFood = () => {
    navigate('/food-menu');
  };

  const dismissMessage = () => {
    setShowMessage(false);
    setTimeout(() => {
      setQuizMessage(null);
    }, 500); // Wait for animation to complete
  };

  return (
    <div style={{
      fontFamily: 'Inter, sans-serif',
      backgroundColor: '#f9fafb',
      minHeight: '100vh'
    }}>
      <style>
        {`
          @keyframes floatAnimation {
            0%, 100% {
              transform: translateY(0);
            }
            50% {
              transform: translateY(-10px);
            }
          }
          @keyframes slideDown {
            0% {
              transform: translateX(-50%) translateY(-100%);
              opacity: 0;
            }
            100% {
              transform: translateX(-50%) translateY(0);
              opacity: 1;
            }
          }
          @keyframes slideUp {
            0% {
              transform: translateX(-50%) translateY(0);
              opacity: 1;
            }
            100% {
              transform: translateX(-50%) translateY(-100%);
              opacity: 0;
            }
          }
        `}
      </style>
      <GlobalHeader />

      {/* Quiz Completion Message */}
      {showMessage && quizMessage && (
        <div style={{
          position: 'fixed',
          top: '90px', // Below the header
          left: '50%',
          transform: 'translateX(-50%)',
          zIndex: 1000,
          maxWidth: '500px',
          width: '90%',
          animation: showMessage ? 'slideDown 0.5s ease-out' : 'slideUp 0.5s ease-out'
        }}>
          <div style={{
            backgroundColor: quizMessage.type === 'success' ? '#d1fae5' :
                           quizMessage.type === 'error' ? '#fee2e2' :
                           quizMessage.type === 'warning' ? '#fef3c7' : '#dbeafe',
            border: `2px solid ${quizMessage.type === 'success' ? '#10b981' :
                                quizMessage.type === 'error' ? '#ef4444' :
                                quizMessage.type === 'warning' ? '#f59e0b' : '#3b82f6'}`,
            borderRadius: '12px',
            padding: '16px 20px',
            boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
            backdropFilter: 'blur(8px)',
            position: 'relative'
          }}>
            <div style={{
              display: 'flex',
              alignItems: 'flex-start',
              justifyContent: 'space-between'
            }}>
              <div style={{ flex: 1 }}>
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  marginBottom: '8px'
                }}>
                  <span style={{
                    fontSize: '20px',
                    marginRight: '8px'
                  }}>
                    {quizMessage.type === 'success' ? '✅' :
                     quizMessage.type === 'error' ? '❌' :
                     quizMessage.type === 'warning' ? '⚠️' : 'ℹ️'}
                  </span>
                  <h3 style={{
                    fontSize: '16px',
                    fontWeight: '600',
                    color: quizMessage.type === 'success' ? '#065f46' :
                           quizMessage.type === 'error' ? '#991b1b' :
                           quizMessage.type === 'warning' ? '#92400e' : '#1e40af',
                    margin: 0
                  }}>
                    {quizMessage.title}
                  </h3>
                </div>
                <p style={{
                  fontSize: '14px',
                  color: quizMessage.type === 'success' ? '#047857' :
                         quizMessage.type === 'error' ? '#dc2626' :
                         quizMessage.type === 'warning' ? '#d97706' : '#2563eb',
                  margin: 0,
                  lineHeight: '1.4'
                }}>
                  {quizMessage.message}
                </p>
              </div>
              <button
                onClick={dismissMessage}
                style={{
                  backgroundColor: 'transparent',
                  border: 'none',
                  color: quizMessage.type === 'success' ? '#065f46' :
                         quizMessage.type === 'error' ? '#991b1b' :
                         quizMessage.type === 'warning' ? '#92400e' : '#1e40af',
                  cursor: 'pointer',
                  fontSize: '18px',
                  padding: '4px',
                  marginLeft: '12px',
                  borderRadius: '4px',
                  transition: 'background-color 0.2s'
                }}
                onMouseEnter={(e) => {
                  e.target.style.backgroundColor = 'rgba(0, 0, 0, 0.1)';
                }}
                onMouseLeave={(e) => {
                  e.target.style.backgroundColor = 'transparent';
                }}
              >
                ×
              </button>
            </div>
          </div>
        </div>
      )}

      <style>
        {`
          /* Mobile-first responsive design with standard breakpoints */

          /* Base styles (mobile 320px+) */
          .responsive-container {
            width: 100%;
            max-width: 1280px;
            margin: 0 auto;
            padding: 5rem 0.75rem 1rem 0.75rem; /* 80px top for fixed header */
            overflow-x: hidden;
          }

          /* Ensure consistent padding across all sections */
          @media screen and (max-width: 768px) {
            .responsive-container {
              padding: 5rem 0.75rem 1rem 0.75rem !important;
            }
          }

          /* Ensure body doesn't have horizontal scroll */
          body {
            overflow-x: hidden !important;
            width: 100% !important;
            max-width: 100% !important;
          }

          /* Hero Section - Mobile First with full width */
          .hero-section {
            background: linear-gradient(135deg, white 0%, rgba(255, 218, 185, 0.3) 100%);
            border-radius: 0.75rem;
            padding: 1rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            width: 100vw;
            position: relative;
            left: 50%;
            right: 50%;
            margin-left: -50vw;
            margin-right: -50vw;
            box-sizing: border-box;
          }

          /* Override for mobile with more specific targeting */
          @media screen and (max-width: 768px) {
            .hero-section {
              width: 100vw !important;
              margin-left: -50vw !important;
              margin-right: -50vw !important;
              left: 50% !important;
              right: 50% !important;
              position: relative !important;
              max-width: none !important;
              min-width: 100vw !important;
              border-radius: 0 !important;
            }
          }

          .hero-content {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            align-items: flex-start;
            max-width: 1280px;
            margin: 0 auto;
            width: 100%;
            padding: 0 1rem;
            box-sizing: border-box;
          }

          /* Mobile-specific content centering */
          @media screen and (max-width: 768px) {
            .hero-content {
              padding: 0 0.75rem !important;
              margin: 0 auto !important;
            }
          }

          .hero-text h1 {
            font-size: 1.375rem; /* 22px */
            font-weight: 700;
            color: #1f2937;
            margin: 0;
            line-height: 1.3;
          }

          .hero-text p {
            font-size: 0.875rem; /* 14px */
            color: #6b7280;
            margin: 0.25rem 0 0 0;
            line-height: 1.4;
          }

          .hero-badge {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(8px);
            padding: 0.5rem 0.75rem;
            border-radius: 1.5rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
            gap: 0.375rem;
            font-size: 0.75rem; /* 12px */
            font-weight: 500;
            color: #374151;
            align-self: flex-start;
          }

          /* Cards Grid - Mobile First */
          .cards-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 1rem;
            margin-bottom: 2rem;
          }

          .card {
            background: white;
            border-radius: 0.875rem;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            transition: all 0.2s ease;
            overflow: hidden;
            border: 1px solid rgba(0, 0, 0, 0.05);
          }

          .card:active {
            transform: scale(0.98);
          }

          .card-content {
            padding: 1rem;
          }

          .card-header {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            gap: 0.75rem;
            margin-bottom: 1rem;
          }

          .card-image {
            width: 64px;
            height: 64px;
            border-radius: 0.75rem;
            overflow: hidden;
            flex-shrink: 0;
          }

          .card-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }

          .card-text h2 {
            font-size: 1.125rem; /* 18px */
            font-weight: 600;
            color: #1f2937;
            margin: 0;
            line-height: 1.3;
          }

          .card-text p {
            color: #6b7280;
            margin: 0.375rem 0 0 0;
            font-size: 0.8125rem; /* 13px */
            line-height: 1.4;
          }

          .features-list {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
            margin-bottom: 1.25rem;
          }

          .feature-item {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            font-size: 0.75rem; /* 12px */
            color: #6b7280;
            padding: 0.25rem;
          }

          .feature-item i {
            font-size: 0.875rem;
            flex-shrink: 0;
          }

          /* Button Styles - Touch Friendly */
          .btn-primary {
            width: 100%;
            min-height: 44px; /* Touch-friendly minimum */
            background: #FF6F61;
            color: white;
            font-weight: 600;
            font-size: 0.875rem;
            padding: 0.75rem 1rem;
            border-radius: 1.5rem;
            border: none;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-bottom: 0.75rem;
          }

          .btn-primary:active {
            background: #e55a4d;
            transform: scale(0.98);
          }

          .btn-secondary {
            width: 100%;
            min-height: 44px;
            background: white;
            border: 2px solid #FF6F61;
            color: #FF6F61;
            font-weight: 600;
            font-size: 0.875rem;
            padding: 0.75rem 1rem;
            border-radius: 1.5rem;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-bottom: 0.75rem;
          }

          .btn-secondary:active {
            background: rgba(255, 111, 97, 0.1);
            transform: scale(0.98);
          }

          .last-result {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            color: #9ca3af;
            font-size: 0.75rem;
            text-align: center;
          }

          /* Insights Section - Mobile First with full width */
          .insights-section {
            background: linear-gradient(135deg, #006D77 0%, #004d56 100%);
            border-radius: 0.875rem;
            padding: 1.25rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 12px rgba(0, 109, 119, 0.2);
            width: 100vw;
            position: relative;
            left: 50%;
            right: 50%;
            margin-left: -50vw;
            margin-right: -50vw;
            box-sizing: border-box;
          }

          /* Override for mobile with more specific targeting */
          @media screen and (max-width: 768px) {
            .insights-section {
              width: 100vw !important;
              margin-left: -50vw !important;
              margin-right: -50vw !important;
              left: 50% !important;
              right: 50% !important;
              position: relative !important;
              max-width: none !important;
              min-width: 100vw !important;
              border-radius: 0 !important;
            }
          }

          .insights-content {
            max-width: 1280px;
            margin: 0 auto;
            width: 100%;
            padding: 0 1rem;
            box-sizing: border-box;
          }

          /* Mobile-specific insights content centering */
          @media screen and (max-width: 768px) {
            .insights-content {
              padding: 0 0.75rem !important;
              margin: 0 auto !important;
              width: calc(100% - 1.5rem) !important;
              max-width: none !important;
            }
          }

          .insights-title {
            font-size: 1.25rem; /* 20px */
            font-weight: 700;
            color: white;
            margin: 0 0 1rem 0;
            text-align: center;
          }

          .stats-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 0.75rem;
            margin-bottom: 1.5rem;
          }

          .stat-card {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(8px);
            border-radius: 0.75rem;
            padding: 1rem;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.1);
          }

          .stat-card h3 {
            font-size: 1.5rem;
            font-weight: 700;
            color: white;
            margin: 0.5rem 0 0.25rem 0;
          }

          .stat-card p {
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.75rem;
            margin: 0;
          }

          /* Trending Section - Mobile First */
          .trending-section {
            margin-bottom: 2rem;
          }

          .trending-title {
            font-size: 1.25rem; /* 20px */
            font-weight: 700;
            color: #1f2937;
            margin: 0 0 1rem 0;
            text-align: center;
          }

          .trending-scroll {
            display: flex;
            overflow-x: auto;
            gap: 0.75rem;
            padding: 0.5rem 0 1rem 0;
            scroll-behavior: smooth;
            -webkit-overflow-scrolling: touch;
          }

          .trending-scroll::-webkit-scrollbar {
            height: 4px;
          }

          .trending-scroll::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 2px;
          }

          .trending-scroll::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 2px;
          }

          .trending-card {
            flex-shrink: 0;
            width: 180px;
            background: white;
            border-radius: 0.75rem;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            overflow: hidden;
            border: 1px solid rgba(0, 0, 0, 0.05);
          }

          .trending-card-image {
            height: 100px;
            position: relative;
            overflow: hidden;
          }

          .trending-card-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }

          .trending-card-content {
            padding: 0.75rem;
          }

          .trending-card h3 {
            font-size: 0.875rem;
            font-weight: 600;
            color: #1f2937;
            margin: 0 0 0.5rem 0;
            line-height: 1.3;
          }

          .trending-card button {
            width: 100%;
            min-height: 36px;
            background: rgba(255, 111, 97, 0.1);
            color: #FF6F61;
            font-weight: 500;
            font-size: 0.75rem;
            padding: 0.5rem;
            border-radius: 0.5rem;
            border: none;
            cursor: pointer;
            transition: all 0.2s ease;
          }

          .trending-card button:active {
            background: rgba(255, 111, 97, 0.2);
            transform: scale(0.98);
          }

          /* Tablet Styles (640px+) */
          @media (min-width: 640px) {
            .responsive-container {
              padding: 5.5rem 1rem 1.5rem 1rem;
            }

            .hero-section {
              padding: 1.5rem;
            }

            .hero-content {
              flex-direction: row;
              justify-content: space-between;
              align-items: center;
            }

            .hero-text h1 {
              font-size: 1.625rem; /* 26px */
            }

            .hero-text p {
              font-size: 1rem; /* 16px */
            }

            .hero-badge {
              font-size: 0.875rem;
              align-self: center;
            }

            .card-header {
              flex-direction: row;
              text-align: left;
              align-items: center;
            }

            .card-image {
              width: 80px;
              height: 80px;
            }

            .card-text h2 {
              font-size: 1.25rem; /* 20px */
            }

            .card-text p {
              font-size: 0.875rem; /* 14px */
            }

            .feature-item {
              justify-content: flex-start;
              font-size: 0.8125rem; /* 13px */
            }

            .stats-grid {
              grid-template-columns: repeat(2, 1fr);
              gap: 1rem;
            }

            .trending-card {
              width: 200px;
            }

            .trending-title,
            .insights-title {
              text-align: left;
            }
          }

          /* Desktop Styles (768px+) */
          @media (min-width: 768px) {
            .responsive-container {
              padding: 6rem 1.5rem 2rem 1.5rem;
            }

            .hero-section {
              padding: 2rem;
            }

            .hero-text h1 {
              font-size: 1.875rem; /* 30px */
            }

            .hero-text p {
              font-size: 1.125rem; /* 18px */
            }

            .cards-grid {
              grid-template-columns: repeat(2, 1fr);
              gap: 1.5rem;
            }

            .card-content {
              padding: 1.5rem;
            }

            .card-image {
              width: 96px;
              height: 96px;
            }

            .card-text h2 {
              font-size: 1.375rem; /* 22px */
            }

            .card-text p {
              font-size: 0.9375rem; /* 15px */
            }

            .feature-item {
              font-size: 0.875rem; /* 14px */
            }

            .insights-section {
              padding: 2rem;
            }

            .insights-title {
              font-size: 1.5rem; /* 24px */
            }

            .stats-grid {
              grid-template-columns: repeat(4, 1fr);
            }

            .trending-title {
              font-size: 1.5rem; /* 24px */
            }

            .trending-card {
              width: 220px;
            }

            .trending-card-image {
              height: 120px;
            }

            /* Hover effects for desktop */
            .card:hover {
              box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
              transform: translateY(-2px);
            }

            .btn-primary:hover {
              background: #e55a4d;
              transform: translateY(-1px);
            }

            .btn-secondary:hover {
              background: rgba(255, 111, 97, 0.05);
              transform: translateY(-1px);
            }

            .trending-card button:hover {
              background: rgba(255, 111, 97, 0.15);
            }
          }

          /* Large Desktop Styles (1024px+) */
          @media (min-width: 1024px) {
            .responsive-container {
              padding: 6.5rem 2rem 2rem 2rem;
            }

            .hero-section {
              padding: 2.5rem;
            }

            .hero-text h1 {
              font-size: 2rem; /* 32px */
            }

            .cards-grid {
              gap: 2rem;
            }

            .card-content {
              padding: 2rem;
            }

            .card-image {
              width: 112px;
              height: 112px;
            }

            .card-text h2 {
              font-size: 1.5rem; /* 24px */
            }

            .card-text p {
              font-size: 1rem; /* 16px */
            }

            .trending-card {
              width: 240px;
            }

            .trending-card-image {
              height: 140px;
            }
          }

          /* Extra Large Screens (1280px+) */
          @media (min-width: 1280px) {
            .responsive-container {
              padding: 7rem 2rem 2rem 2rem;
            }

            .hero-section {
              padding: 3rem;
            }

            .cards-grid {
              gap: 2.5rem;
            }

            .trending-card {
              width: 260px;
            }
          }
        `}
      </style>

      <main className="responsive-container">
        {/* Hero Welcome Section */}
        <section className="hero-section">
          <div className="hero-content">
            <div className="hero-text">
              <h1>Welcome back, {userName}!</h1>
              <p>Ready to discover your perfect meal match today?</p>
              <p style={{
                color: '#FF6F61',
                fontWeight: '500',
                marginTop: '8px',
                fontSize: '14px'
              }}>
                <i className="fa-solid fa-sun" style={{ marginRight: '4px' }}></i> Good afternoon! How are you feeling?
              </p>
            </div>
            <div className="hero-badge">
              <i className="fa-solid fa-medal" style={{ color: '#f59e0b' }}></i>
              <span>5 days of mindful eating</span>
            </div>
          </div>
        </section>

        {/* Main Choice Section */}
        <section className="cards-grid">
          {/* Left Card - Mood Quiz */}
          <div className="card">
            <div className="card-content">
              <div className="card-header">
                <div className="card-image">
                  <img
                    src="https://storage.googleapis.com/uxpilot-auth.appspot.com/597fca130f-890ed68c096d4a9dc8be.png"
                    alt="person meditating with colorful mood bubbles floating around, minimalist illustration, peaceful"
                  />
                </div>
                <div className="card-text">
                  <h2>Discover Through Your Mood</h2>
                  <p>Take our 5-minute personalized assessment to find food that matches your emotional state right now</p>
                </div>
              </div>
              <div className="features-list">
                <div className="feature-item">
                  <i className="fa-solid fa-sparkles" style={{ color: '#fbbf24' }}></i>
                  <span>100+ emotional states covered</span>
                </div>
                <div className="feature-item">
                  <i className="fa-solid fa-book" style={{ color: '#006D77' }}></i>
                  <span>Get a matching book recommendation</span>
                </div>
                <div className="feature-item">
                  <i className="fa-solid fa-bullseye" style={{ color: '#FF6F61' }}></i>
                  <span>Scientifically personalized results</span>
                </div>
              </div>
              <button
                onClick={handleStartQuiz}
                className="btn-primary"
              >
                Start Mood Quiz
              </button>
              <div className="last-result">
                <i className="fa-solid fa-clock-rotate-left"></i>
                <span>Last result: Comfort Food for Relaxation</span>
              </div>
            </div>
          </div>

          {/* Right Card - Direct Menu */}
          <div className="card">
            <div className="card-content">
              <div className="card-header">
                <div className="card-image">
                  <img
                    src="https://storage.googleapis.com/uxpilot-auth.appspot.com/4fbfadd89a-1d8df480d55f4db785c2.png"
                    alt="beautiful spread of Indian vegetarian comfort food with soft lighting, rajma chawal, paneer, thali"
                  />
                </div>
                <div className="card-text">
                  <h2>Browse Our Menu</h2>
                  <p>Explore our curated collection of vegetarian comfort foods, organized by mood and craving type</p>
                </div>
              </div>
              <div className="features-list">
                <div className="feature-item">
                  <i className="fa-solid fa-seedling" style={{ color: '#10b981' }}></i>
                  <span>100% vegetarian cuisine</span>
                </div>
                <div className="feature-item">
                  <i className="fa-solid fa-heart" style={{ color: '#ef4444' }}></i>
                  <span>Comfort food specialists</span>
                </div>
                <div className="feature-item">
                  <i className="fa-solid fa-tag" style={{ color: '#3b82f6' }}></i>
                  <span>Mood-tagged recipes</span>
                </div>
              </div>
              <button
                onClick={handleExploreFood}
                className="btn-secondary"
              >
                Explore Menu
              </button>
              <div className="last-result">
                <i className="fa-solid fa-star" style={{ color: '#fbbf24' }}></i>
                <span>Popular today: Rajma-Chawal Bowl</span>
              </div>
            </div>
          </div>
        </section>

        {/* Quick Actions Bar */}
        <section style={{
          marginBottom: '40px'
        }}>
          <div style={{
            backgroundColor: 'white',
            borderRadius: '16px',
            boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
            padding: '16px'
          }}>
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(3, 1fr)',
              gap: '16px'
            }}>
              <button style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: '12px',
                padding: '12px 16px',
                borderRadius: '12px',
                backgroundColor: 'transparent',
                border: 'none',
                cursor: 'pointer',
                transition: 'background-color 0.3s ease'
              }} onMouseEnter={(e) => e.target.style.backgroundColor = '#f9fafb'}
                 onMouseLeave={(e) => e.target.style.backgroundColor = 'transparent'}>
                <div style={{
                  width: '40px',
                  height: '40px',
                  backgroundColor: '#FFDAB9',
                  borderRadius: '50%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <i className="fa-solid fa-utensils" style={{ color: '#FF6F61' }}></i>
                </div>
                <span style={{
                  fontWeight: '500',
                  color: '#374151'
                }}>Repeat Last Order</span>
              </button>

              <button style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: '12px',
                padding: '12px 16px',
                borderRadius: '12px',
                backgroundColor: 'transparent',
                border: 'none',
                cursor: 'pointer',
                transition: 'background-color 0.3s ease'
              }} onMouseEnter={(e) => e.target.style.backgroundColor = '#f9fafb'}
                 onMouseLeave={(e) => e.target.style.backgroundColor = 'transparent'}>
                <div style={{
                  width: '40px',
                  height: '40px',
                  backgroundColor: '#FFDAB9',
                  borderRadius: '50%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <i className="fa-solid fa-dice" style={{ color: '#FF6F61' }}></i>
                </div>
                <span style={{
                  fontWeight: '500',
                  color: '#374151'
                }}>Feeling Lucky</span>
              </button>

              <button style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: '12px',
                padding: '12px 16px',
                borderRadius: '12px',
                backgroundColor: 'transparent',
                border: 'none',
                cursor: 'pointer',
                transition: 'background-color 0.3s ease'
              }} onMouseEnter={(e) => e.target.style.backgroundColor = '#f9fafb'}
                 onMouseLeave={(e) => e.target.style.backgroundColor = 'transparent'}>
                <div style={{
                  width: '40px',
                  height: '40px',
                  backgroundColor: '#FFDAB9',
                  borderRadius: '50%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <i className="fa-solid fa-clock-rotate-left" style={{ color: '#FF6F61' }}></i>
                </div>
                <span style={{
                  fontWeight: '500',
                  color: '#374151'
                }}>View History</span>
              </button>
            </div>
          </div>
        </section>

        {/* Mood Insights Section */}
        <section className="insights-section">
          <div className="insights-content">
            <h2 className="insights-title">Your Journey So Far</h2>
            <div className="stats-grid">
            <div className="stat-card">
              <div style={{
                width: '48px',
                height: '48px',
                margin: '0 auto',
                backgroundColor: 'rgba(255, 255, 255, 0.2)',
                borderRadius: '50%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                marginBottom: '8px'
              }}>
                <i className="fa-solid fa-clipboard-question" style={{ color: 'white', fontSize: '20px' }}></i>
              </div>
              <h3 style={{
                fontSize: '24px',
                fontWeight: 'bold',
                color: 'white',
                margin: '0 0 4px 0'
              }}>12</h3>
              <p style={{
                color: 'rgba(255, 255, 255, 0.8)',
                fontSize: '14px',
                margin: 0
              }}>Quizzes Taken</p>
            </div>

            <div className="stat-card">
              <div style={{
                width: '48px',
                height: '48px',
                margin: '0 auto',
                backgroundColor: 'rgba(255, 255, 255, 0.2)',
                borderRadius: '50%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                marginBottom: '8px'
              }}>
                <i className="fa-solid fa-face-smile" style={{ color: 'white', fontSize: '20px' }}></i>
              </div>
              <h3 style={{
                fontSize: '24px',
                fontWeight: 'bold',
                color: 'white',
                margin: '0 0 4px 0'
              }}>8</h3>
              <p style={{
                color: 'rgba(255, 255, 255, 0.8)',
                fontSize: '14px',
                margin: 0
              }}>Different Moods</p>
            </div>

            <div className="stat-card">
              <div style={{
                width: '48px',
                height: '48px',
                margin: '0 auto',
                backgroundColor: 'rgba(255, 255, 255, 0.2)',
                borderRadius: '50%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                marginBottom: '8px'
              }}>
                <i className="fa-solid fa-plate-wheat" style={{ color: 'white', fontSize: '20px' }}></i>
              </div>
              <h3 style={{
                fontSize: '24px',
                fontWeight: 'bold',
                color: 'white',
                margin: '0 0 4px 0'
              }}>24</h3>
              <p style={{
                color: 'rgba(255, 255, 255, 0.8)',
                fontSize: '14px',
                margin: 0
              }}>Meals Matched</p>
            </div>

            <div className="stat-card">
              <div style={{
                width: '48px',
                height: '48px',
                margin: '0 auto',
                backgroundColor: 'rgba(255, 255, 255, 0.2)',
                borderRadius: '50%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                marginBottom: '8px'
              }}>
                <i className="fa-solid fa-book" style={{ color: 'white', fontSize: '20px' }}></i>
              </div>
              <h3 style={{
                fontSize: '24px',
                fontWeight: 'bold',
                color: 'white',
                margin: '0 0 4px 0'
              }}>5</h3>
              <p style={{
                color: 'rgba(255, 255, 255, 0.8)',
                fontSize: '14px',
                margin: 0
              }}>Books Discovered</p>
            </div>
          </div>

          <div style={{
            backgroundColor: 'rgba(255, 255, 255, 0.1)',
            backdropFilter: 'blur(4px)',
            borderRadius: '12px',
            padding: '16px'
          }}>
            <div style={{
              display: 'flex',
              alignItems: 'center'
            }}>
              <i className="fa-solid fa-calendar-day" style={{ color: 'white', marginRight: '12px' }}></i>
              <p style={{
                color: 'white',
                margin: 0
              }}>
                <span style={{ fontWeight: '500' }}>Yesterday:</span> Overthinking → Tomato Soup + 'The Power of Now'
              </p>
            </div>
          </div>
          </div>
        </section>

        {/* Recommendation Preview */}
        <section className="trending-section">
          <h2 className="trending-title">Trending Mood Matches Today</h2>
          <div className="trending-scroll">
            <div className="trending-card">
              <div className="trending-card-image">
                <img
                  src="https://storage.googleapis.com/uxpilot-auth.appspot.com/b155e559f0-671bc6cf4e61c2f4ae72.png"
                  alt="rajma chawal bowl indian comfort food"
                />
                <div style={{
                  position: 'absolute',
                  top: '8px',
                  left: '8px',
                  backgroundColor: 'rgba(255, 255, 255, 0.9)',
                  backdropFilter: 'blur(4px)',
                  borderRadius: '12px',
                  padding: '4px 8px',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '4px'
                }}>
                  <i className="fa-solid fa-brain" style={{ color: '#ef4444', fontSize: '10px' }}></i>
                  <span style={{
                    fontSize: '10px',
                    fontWeight: '500',
                    color: '#374151'
                  }}>Feeling Stressed</span>
                </div>
              </div>
              <div className="trending-card-content">
                <h3>Rajma-Chawal Bowl</h3>
                <button>Try This Match</button>
              </div>
            </div>

            <div className="trending-card">
              <div className="trending-card-image">
                <img
                  src="https://storage.googleapis.com/uxpilot-auth.appspot.com/79a4a0abac-55884704315b62ee6f30.png"
                  alt="paneer paratha indian food"
                />
                <div style={{
                  position: 'absolute',
                  top: '8px',
                  left: '8px',
                  backgroundColor: 'rgba(255, 255, 255, 0.9)',
                  backdropFilter: 'blur(4px)',
                  borderRadius: '12px',
                  padding: '4px 8px',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '4px'
                }}>
                  <i className="fa-solid fa-bolt" style={{ color: '#eab308', fontSize: '10px' }}></i>
                  <span style={{
                    fontSize: '10px',
                    fontWeight: '500',
                    color: '#374151'
                  }}>Need Energy</span>
                </div>
              </div>
              <div className="trending-card-content">
                <h3>Paneer Paratha</h3>
                <button>Try This Match</button>
              </div>
            </div>

            <div className="trending-card">
              <div className="trending-card-image">
                <img
                  src="https://storage.googleapis.com/uxpilot-auth.appspot.com/29ac1ddad8-f8891af7d8badfe132ef.png"
                  alt="masala chai indian tea with spices"
                />
                <div style={{
                  position: 'absolute',
                  top: '8px',
                  left: '8px',
                  backgroundColor: 'rgba(255, 255, 255, 0.9)',
                  backdropFilter: 'blur(4px)',
                  borderRadius: '12px',
                  padding: '4px 8px',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '4px'
                }}>
                  <i className="fa-solid fa-hand-holding-heart" style={{ color: '#a855f7', fontSize: '10px' }}></i>
                  <span style={{
                    fontSize: '10px',
                    fontWeight: '500',
                    color: '#374151'
                  }}>Comfort Seeking</span>
                </div>
              </div>
              <div className="trending-card-content">
                <h3>Masala Milk Tea</h3>
                <button>Try This Match</button>
              </div>
            </div>

            <div className="trending-card">
              <div className="trending-card-image">
                <img
                  src="https://storage.googleapis.com/uxpilot-auth.appspot.com/a25e7285b0-a2e86672145868da2945.png"
                  alt="dal tadka indian lentil soup"
                />
                <div style={{
                  position: 'absolute',
                  top: '8px',
                  left: '8px',
                  backgroundColor: 'rgba(255, 255, 255, 0.9)',
                  backdropFilter: 'blur(4px)',
                  borderRadius: '12px',
                  padding: '4px 8px',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '4px'
                }}>
                  <i className="fa-solid fa-cloud-rain" style={{ color: '#3b82f6', fontSize: '10px' }}></i>
                  <span style={{
                    fontSize: '10px',
                    fontWeight: '500',
                    color: '#374151'
                  }}>Feeling Gloomy</span>
                </div>
              </div>
              <div className="trending-card-content">
                <h3>Dal Tadka</h3>
                <button>Try This Match</button>
              </div>
            </div>
          </div>
        </section>
      </main>

      <GlobalFooter />
    </div>
  );
}

export default MainMenu;
