<?php
namespace App\Http\Controllers\API;

use App\Models\FoodItem;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class FoodItemController extends Controller
{
    public function index()
    {
        return response()->json(FoodItem::with('mood')->get());
    }

    public function store(Request $request)
    {
        $request->validate([
            'mood_id' => 'required|exists:moods,id',
            'name' => 'required|string',
            'type' => 'required|in:veg,non-veg',
            'image' => 'nullable|string', // for now, accepting image URL or base64
            'description' => 'nullable|string',
        ]);

        $food = FoodItem::create($request->all());

        return response()->json($food->load('mood'), 201);
    }

    public function show($id)
    {
        return response()->json(FoodItem::with('mood')->findOrFail($id));
    }

    public function update(Request $request, $id)
    {
        $food = FoodItem::findOrFail($id);
        $food->update($request->all());

        return response()->json($food->load('mood'));
    }

    public function destroy($id)
    {
        FoodItem::findOrFail($id)->delete();
        return response()->json(['message' => 'Food item deleted']);
    }
}
