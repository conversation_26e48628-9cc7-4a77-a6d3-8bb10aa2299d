import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { moodsAPI, subMoodsAPI } from '../../services/api';
import DashboardSidebar from '../../components/DashboardSidebar';

function Moods() {
  const navigate = useNavigate();
  const [moods, setMoods] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedMood, setSelectedMood] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [userName, setUserName] = useState('Aisha');

  // CRUD form states
  const [showCrudForm, setShowCrudForm] = useState(false);
  const [formMode, setFormMode] = useState('add'); // 'add', 'edit'
  const [editingMood, setEditingMood] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    sub_moods: []
  });
  const [formLoading, setFormLoading] = useState(false);
  const [formMessage, setFormMessage] = useState('');

  // Sub-mood form states
  const [newSubMood, setNewSubMood] = useState({ name: '', meaning: '' });
  const [editingSubMoodIndex, setEditingSubMoodIndex] = useState(-1);

  // Form field focus states
  const [nameFocused, setNameFocused] = useState(false);
  const [subMoodNameFocused, setSubMoodNameFocused] = useState(false);
  const [subMoodMeaningFocused, setSubMoodMeaningFocused] = useState(false);

  // Form field hover states
  const [nameHovered, setNameHovered] = useState(false);
  const [submitHovered, setSubmitHovered] = useState(false);
  const [cancelHovered, setCancelHovered] = useState(false);

  // Tooltip state
  const [tooltipVisible, setTooltipVisible] = useState(null);
  const [tooltipPosition, setTooltipPosition] = useState({ x: 0, y: 0 });

  // Tooltip functions
  const showTooltip = (moodId, event) => {
    const rect = event.target.getBoundingClientRect();
    setTooltipPosition({
      x: rect.left + rect.width / 2,
      y: rect.top - 10
    });
    setTooltipVisible(moodId);
  };

  const hideTooltip = () => {
    setTooltipVisible(null);
  };

  // Mood categories
  const moodCategories = [
    { id: 'all', name: 'All Moods', color: '#6b7280' },
    { id: 'stressed', name: 'Feeling Stressed', color: '#ef4444' },
    { id: 'energy', name: 'Need Energy', color: '#eab308' },
    { id: 'comfort', name: 'Seeking Comfort', color: '#a855f7' },
    { id: 'focus', name: 'Need Focus', color: '#3b82f6' },
    { id: 'calm', name: 'Want Calm', color: '#10b981' }
  ];

  useEffect(() => {
    // Get user data from localStorage
    const storedUser = localStorage.getItem('user');
    if (storedUser) {
      try {
        const userData = JSON.parse(storedUser);
        if (userData && userData.name) {
          setUserName(userData.name);
        }
      } catch (error) {
        console.error('Error parsing user data:', error);
      }
    }
    
    fetchMoods();
  }, []);

  const fetchMoods = async () => {
    try {
      setLoading(true);
      const data = await moodsAPI.getAll();
      setMoods(data);
      setError('');
    } catch (error) {
      console.error('Error fetching moods:', error);
      setError(error.message || 'Error loading moods');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setFormLoading(true);
    setFormMessage('');

    try {
      if (formMode === 'add') {
        await moodsAPI.create(formData);
        setFormMessage('Mood added successfully!');
      } else {
        await moodsAPI.update(editingMood.id, formData);
        setFormMessage('Mood updated successfully!');
      }

      // Refresh moods list
      await fetchMoods();

      // Reset form after short delay
      setTimeout(() => {
        resetForm();
        setShowCrudForm(false);
      }, 1500);
    } catch (error) {
      console.error('Error saving mood:', error);
      setFormMessage(error.message || 'Error saving mood');
    } finally {
      setFormLoading(false);
    }
  };



  const handleDeleteMood = async (moodId) => {
    if (window.confirm('Are you sure you want to delete this mood?')) {
      try {
        await moodsAPI.delete(moodId);
        await fetchMoods(); // Refresh the list
      } catch (error) {
        console.error('Error deleting mood:', error);
        alert(error.message || 'Error deleting mood');
      }
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      sub_moods: []
    });
    setEditingMood(null);
    setFormMode('add');
    setFormMessage('');
    setNewSubMood({ name: '', meaning: '' });
    setEditingSubMoodIndex(-1);
  };

  // Filter moods
  const filteredMoods = moods.filter(mood => {
    const matchesSearch = (mood.name || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (mood.sub_moods || []).some(subMood =>
                           (subMood.name || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
                           (subMood.meaning || '').toLowerCase().includes(searchTerm.toLowerCase())
                         );

    if (selectedMood === 'all') return matchesSearch;

    const matchesMood = mood.sub_moods && mood.sub_moods.some(subMood =>
      (subMood.name || '').toLowerCase().includes(selectedMood) ||
      (subMood.meaning || '').toLowerCase().includes(selectedMood)
    );

    return matchesSearch && matchesMood;
  });

  const getMoodColor = (moodName) => {
    if (!moodName || typeof moodName !== 'string') {
      return '#6b7280';
    }
    const mood = moodCategories.find(m =>
      moodName.toLowerCase().includes(m.id) ||
      m.name.toLowerCase().includes(moodName.toLowerCase())
    );
    return mood ? mood.color : '#6b7280';
  };

  // Sub-mood management functions
  const addSubMood = () => {
    if (newSubMood.name.trim() && newSubMood.meaning.trim()) {
      const updatedSubMoods = [...formData.sub_moods, {
        id: Date.now(), // Temporary ID for new sub-moods
        name: newSubMood.name.trim(),
        meaning: newSubMood.meaning.trim()
      }];
      setFormData({...formData, sub_moods: updatedSubMoods});
      setNewSubMood({ name: '', meaning: '' });
    }
  };

  const editSubMood = (index) => {
    const subMood = formData.sub_moods[index];
    setNewSubMood({ name: subMood.name, meaning: subMood.meaning });
    setEditingSubMoodIndex(index);
  };

  const updateSubMood = () => {
    if (newSubMood.name.trim() && newSubMood.meaning.trim() && editingSubMoodIndex >= 0) {
      const updatedSubMoods = [...formData.sub_moods];
      updatedSubMoods[editingSubMoodIndex] = {
        ...updatedSubMoods[editingSubMoodIndex],
        name: newSubMood.name.trim(),
        meaning: newSubMood.meaning.trim()
      };
      setFormData({...formData, sub_moods: updatedSubMoods});
      setNewSubMood({ name: '', meaning: '' });
      setEditingSubMoodIndex(-1);
    }
  };

  const deleteSubMood = (index) => {
    const updatedSubMoods = formData.sub_moods.filter((_, i) => i !== index);
    setFormData({...formData, sub_moods: updatedSubMoods});
  };

  const cancelSubMoodEdit = () => {
    setNewSubMood({ name: '', meaning: '' });
    setEditingSubMoodIndex(-1);
  };

  // CRUD Functions
  const handleAdd = () => {
    setFormMode('add');
    setEditingMood(null);
    setFormData({
      name: '',
      sub_moods: []
    });
    setNewSubMood({ name: '', meaning: '' });
    setEditingSubMoodIndex(-1);
    setFormMessage('');
    setShowCrudForm(true);
  };

  const handleEditMood = (mood) => {
    setFormMode('edit');
    setEditingMood(mood);
    setFormData({
      name: mood.name || '',
      sub_moods: mood.sub_moods || []
    });
    setNewSubMood({ name: '', meaning: '' });
    setEditingSubMoodIndex(-1);
    setFormMessage('');
    setShowCrudForm(true);
  };





  const handleFormCancel = () => {
    setShowCrudForm(false);
    setFormData({
      name: '',
      sub_moods: []
    });
    setNewSubMood({ name: '', meaning: '' });
    setEditingSubMoodIndex(-1);
    setFormMessage('');
  };

  return (
    <div style={{ backgroundColor: '#F5E8C7', fontFamily: 'Inter, sans-serif', minHeight: '100vh' }}>
      {/* Dashboard Sidebar */}
      <DashboardSidebar />

      {/* Main Content Area */}
      <div style={{
        paddingLeft: '256px',
        minHeight: 'calc(100vh - 140px)'
      }}>
        <div style={{ maxWidth: '1280px', margin: '0 auto', padding: '16px 24px' }}>
          {/* Welcome Section */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: '2fr 1fr',
            gap: '24px',
            marginBottom: '24px'
          }}>
            <div>
              <div style={{
                backgroundColor: 'white',
                borderRadius: '12px',
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                padding: '24px'
              }}>
                <h1 style={{
                  fontSize: '30px',
                  fontWeight: 'bold',
                  color: '#1f2937'
                }}>Mood Explorer for {userName}!</h1>
                <p style={{ color: '#4b5563', marginTop: '4px' }}>Discover and explore different moods and their sub-moods</p>

                {/* Search Bar */}
                <div style={{ position: 'relative', marginTop: '16px', maxWidth: '100%' }}>
                  <input
                    type="text"
                    placeholder="Search moods by name or sub-mood descriptions..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    style={{
                      width: '100%',
                      maxWidth: '100%',
                      padding: '12px 40px 12px 16px',
                      border: '1px solid rgba(0, 109, 119, 0.3)',
                      borderRadius: '8px',
                      fontSize: '14px',
                      outline: 'none',
                      backgroundColor: '#f9fafb',
                      boxSizing: 'border-box'
                    }}
                  />
                  <i className="fa-solid fa-search" style={{
                    position: 'absolute',
                    right: '12px',
                    top: '50%',
                    transform: 'translateY(-50%)',
                    color: 'rgba(0, 109, 119, 0.6)'
                  }}></i>
                </div>

                {/* Mood Filter Tabs */}
                <div style={{
                  display: 'flex',
                  flexWrap: 'wrap',
                  gap: '8px',
                  marginTop: '16px'
                }}>
                  {moodCategories.map(mood => (
                    <button
                      key={mood.id}
                      onClick={() => setSelectedMood(mood.id)}
                      style={{
                        padding: '6px 12px',
                        borderRadius: '9999px',
                        border: 'none',
                        cursor: 'pointer',
                        fontSize: '12px',
                        fontWeight: '500',
                        backgroundColor: selectedMood === mood.id ? mood.color : `${mood.color}20`,
                        color: selectedMood === mood.id ? 'white' : mood.color
                      }}
                    >
                      {mood.name}
                    </button>
                  ))}
                </div>

                {/* Add New Mood Button */}
                <div style={{ marginTop: '16px' }}>
                  <button
                    onClick={() => handleAdd()}
                    style={{
                      padding: '12px 24px',
                      backgroundColor: '#FF6F61',
                      color: 'white',
                      border: 'none',
                      borderRadius: '8px',
                      fontSize: '14px',
                      fontWeight: '500',
                      cursor: 'pointer',
                      transition: 'all 0.2s ease',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px'
                    }}
                    onMouseEnter={(e) => e.target.style.backgroundColor = 'rgba(255, 111, 97, 0.9)'}
                    onMouseLeave={(e) => e.target.style.backgroundColor = '#FF6F61'}
                  >
                    <i className="fa-solid fa-plus"></i>
                    Add New Mood
                  </button>
                </div>
              </div>
            </div>

            <div>
              <div style={{
                backgroundColor: 'white',
                borderRadius: '12px',
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                padding: '24px'
              }}>
                <h2 style={{ fontSize: '18px', fontWeight: '600', color: '#1f2937', marginBottom: '16px' }}>Mood Stats</h2>
                <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
                  <div style={{ textAlign: 'center' }}>
                    <div style={{
                      width: '48px',
                      height: '48px',
                      backgroundColor: 'rgba(0, 109, 119, 0.1)',
                      borderRadius: '50%',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      margin: '0 auto 8px'
                    }}>
                      <i className="fa-solid fa-heart" style={{ color: '#006D77', fontSize: '20px' }}></i>
                    </div>
                    <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#1f2937' }}>{moods.length}</div>
                    <div style={{ fontSize: '14px', color: '#6b7280' }}>Total Moods</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Moods Display Section */}
          <div style={{ marginBottom: '32px' }}>
            {loading && (
              <div style={{
                backgroundColor: 'white',
                borderRadius: '12px',
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                padding: '48px',
                textAlign: 'center'
              }}>
                <i className="fa-solid fa-spinner fa-spin" style={{ fontSize: '32px', color: '#FF6F61', marginBottom: '16px' }}></i>
                <p style={{ color: '#4b5563', fontSize: '16px' }}>Loading your mood explorer...</p>
              </div>
            )}

            {error && (
              <div style={{
                backgroundColor: 'white',
                borderRadius: '12px',
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                padding: '48px',
                textAlign: 'center'
              }}>
                <i className="fa-solid fa-exclamation-triangle" style={{ fontSize: '32px', color: '#ef4444', marginBottom: '16px' }}></i>
                <p style={{ color: '#ef4444', fontSize: '16px', marginBottom: '16px' }}>{error}</p>
                <button
                  onClick={fetchMoods}
                  style={{
                    backgroundColor: '#FF6F61',
                    color: 'white',
                    padding: '12px 24px',
                    borderRadius: '8px',
                    border: 'none',
                    cursor: 'pointer',
                    fontSize: '14px',
                    fontWeight: '500'
                  }}
                >
                  Try Again
                </button>
              </div>
            )}

            {!loading && !error && (
              <div>
                <div style={{
                  backgroundColor: 'white',
                  borderRadius: '12px',
                  boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                  padding: '24px',
                  marginBottom: '24px'
                }}>
                  <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '16px' }}>
                    <h2 style={{ fontSize: '20px', fontWeight: '600', color: '#1f2937' }}>
                      {filteredMoods.length} Mood{filteredMoods.length !== 1 ? 's' : ''} Found
                    </h2>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                      <div style={{
                        padding: '4px 12px',
                        backgroundColor: 'rgba(0, 109, 119, 0.1)',
                        color: '#006D77',
                        borderRadius: '9999px',
                        fontSize: '14px'
                      }}>
                        {selectedMood === 'all' ? 'All Categories' : moodCategories.find(m => m.id === selectedMood)?.name}
                      </div>
                    </div>
                  </div>

                  {filteredMoods.length === 0 ? (
                    <div style={{ textAlign: 'center', padding: '48px' }}>
                      <i className="fa-solid fa-heart" style={{ fontSize: '48px', color: '#d1d5db', marginBottom: '16px' }}></i>
                      <h3 style={{ fontSize: '18px', fontWeight: '600', color: '#374151', marginBottom: '8px' }}>
                        No moods found
                      </h3>
                      <p style={{ color: '#6b7280', marginBottom: '24px' }}>
                        {searchTerm ? 'Try adjusting your search terms or filters.' : 'No moods available at the moment.'}
                      </p>
                      {searchTerm && (
                        <button
                          onClick={() => {
                            setSearchTerm('');
                            setSelectedMood('all');
                          }}
                          style={{
                            backgroundColor: '#FF6F61',
                            color: 'white',
                            padding: '12px 24px',
                            borderRadius: '8px',
                            border: 'none',
                            cursor: 'pointer',
                            fontSize: '14px',
                            fontWeight: '500'
                          }}
                        >
                          Clear Filters
                        </button>
                      )}
                    </div>
                  ) : (
                    <div style={{
                      display: 'grid',
                      gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',
                      gap: '24px'
                    }}>
                      {filteredMoods.map(mood => (
                        <div
                          key={mood.id}
                          style={{
                            backgroundColor: 'white',
                            borderRadius: '12px',
                            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                            overflow: 'hidden',
                            transition: 'transform 0.2s ease, box-shadow 0.2s ease',
                            cursor: 'pointer',
                            minHeight: '400px',
                            display: 'flex',
                            flexDirection: 'column'
                          }}
                          onMouseEnter={(e) => {
                            e.currentTarget.style.transform = 'translateY(-4px)';
                            e.currentTarget.style.boxShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.1)';
                          }}
                          onMouseLeave={(e) => {
                            e.currentTarget.style.transform = 'translateY(0)';
                            e.currentTarget.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1)';
                          }}
                        >
                          <div style={{
                            height: '160px',
                            background: 'linear-gradient(135deg, #006D77 0%, #83C5BE 100%)',
                            backgroundSize: 'cover',
                            backgroundPosition: 'center',
                            position: 'relative',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center'
                          }}>
                            <i className="fa-solid fa-heart" style={{ fontSize: '48px', color: 'white', opacity: 0.8 }}></i>
                          </div>

                          <div style={{ padding: '20px', flex: 1, display: 'flex', flexDirection: 'column' }}>
                            <h3 style={{
                              fontSize: '20px',
                              fontWeight: '600',
                              color: '#1f2937',
                              marginBottom: '8px'
                            }}>
                              {mood.name || 'Untitled Mood'}
                            </h3>

                            <div style={{
                              fontSize: '14px',
                              color: '#6b7280',
                              marginBottom: '16px'
                            }}>
                              {mood.sub_moods ? mood.sub_moods.length : 0} sub-mood{mood.sub_moods && mood.sub_moods.length !== 1 ? 's' : ''}
                            </div>

                            {mood.sub_moods && mood.sub_moods.length > 0 && (
                              <div style={{ marginBottom: '16px' }}>
                                <div style={{
                                  display: 'flex',
                                  flexWrap: 'wrap',
                                  gap: '6px'
                                }}>
                                  {mood.sub_moods.slice(0, 8).map(subMood => (
                                    <span
                                      key={subMood.id || Math.random()}
                                      style={{
                                        padding: '4px 10px',
                                        backgroundColor: `${getMoodColor(subMood.name || '')}20`,
                                        color: getMoodColor(subMood.name || ''),
                                        borderRadius: '16px',
                                        fontSize: '12px',
                                        fontWeight: '500'
                                      }}
                                      title={subMood.meaning || 'No description available'}
                                    >
                                      {subMood.name || 'Unknown Sub-mood'}
                                    </span>
                                  ))}
                                  {mood.sub_moods.length > 8 && (
                                    <span
                                      style={{
                                        padding: '4px 10px',
                                        backgroundColor: '#f3f4f6',
                                        color: '#6b7280',
                                        borderRadius: '16px',
                                        fontSize: '12px',
                                        fontWeight: '500',
                                        cursor: 'pointer',
                                        position: 'relative'
                                      }}
                                      onMouseEnter={(e) => showTooltip(mood.id, e)}
                                      onMouseLeave={hideTooltip}
                                    >
                                      +{mood.sub_moods.length - 8}
                                    </span>
                                  )}
                                </div>
                              </div>
                            )}

                            {/* Action Buttons */}
                            <div style={{
                              marginTop: 'auto',
                              paddingTop: '16px',
                              display: 'flex',
                              gap: '8px'
                            }}>
                              <button
                                onClick={() => handleEditMood(mood)}
                                style={{
                                  flex: 1,
                                  padding: '8px 16px',
                                  backgroundColor: '#006D77',
                                  color: 'white',
                                  border: 'none',
                                  borderRadius: '6px',
                                  fontSize: '14px',
                                  fontWeight: '500',
                                  cursor: 'pointer',
                                  transition: 'all 0.2s ease'
                                }}
                                onMouseEnter={(e) => e.target.style.backgroundColor = '#005a63'}
                                onMouseLeave={(e) => e.target.style.backgroundColor = '#006D77'}
                              >
                                Edit
                              </button>
                              <button
                                onClick={() => handleDeleteMood(mood.id)}
                                style={{
                                  flex: 1,
                                  padding: '8px 16px',
                                  backgroundColor: '#dc2626',
                                  color: 'white',
                                  border: 'none',
                                  borderRadius: '6px',
                                  fontSize: '14px',
                                  fontWeight: '500',
                                  cursor: 'pointer',
                                  transition: 'all 0.2s ease'
                                }}
                                onMouseEnter={(e) => e.target.style.backgroundColor = '#b91c1c'}
                                onMouseLeave={(e) => e.target.style.backgroundColor = '#dc2626'}
                              >
                                Delete
                              </button>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* CRUD Form Modal */}
      {showCrudForm && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000,
          padding: '20px'
        }}>
          <div style={{
            backgroundColor: 'white',
            borderRadius: '12px',
            padding: '32px',
            width: '100%',
            maxWidth: '500px',
            boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1)',
            position: 'relative'
          }}>
            <h2 style={{
              fontSize: '24px',
              fontWeight: '600',
              color: '#1f2937',
              marginBottom: '24px',
              textAlign: 'center'
            }}>
              {formMode === 'add' ? 'Add New Mood' : 'Edit Mood'}
            </h2>

            <form onSubmit={handleSubmit}>
              {/* Mood Name Field */}
              <div style={{ marginBottom: '24px' }}>
                <label style={{
                  display: 'block',
                  fontSize: '14px',
                  fontWeight: '500',
                  color: '#374151',
                  marginBottom: '8px'
                }}>
                  Mood Name *
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData({...formData, name: e.target.value})}
                  onFocus={() => setNameFocused(true)}
                  onBlur={() => setNameFocused(false)}
                  onMouseEnter={() => setNameHovered(true)}
                  onMouseLeave={() => setNameHovered(false)}
                  required
                  style={{
                    width: '100%',
                    padding: '12px 16px',
                    border: `2px solid ${nameFocused ? '#006D77' : (nameHovered ? 'rgba(0, 109, 119, 0.3)' : '#e5e7eb')}`,
                    borderRadius: '8px',
                    fontSize: '14px',
                    outline: 'none',
                    transition: 'all 0.2s ease',
                    backgroundColor: nameFocused ? '#f0fdfa' : 'white',
                    boxSizing: 'border-box'
                  }}
                  placeholder="Enter mood name (e.g., Happy, Sad, Excited)"
                />
              </div>

              {/* Sub-moods Section */}
              <div style={{ marginBottom: '24px' }}>
                <label style={{
                  display: 'block',
                  fontSize: '14px',
                  fontWeight: '500',
                  color: '#374151',
                  marginBottom: '12px'
                }}>
                  Sub-moods ({formData.sub_moods.length})
                </label>

                {/* Add/Edit Sub-mood Form */}
                <div style={{
                  backgroundColor: '#f9fafb',
                  padding: '16px',
                  borderRadius: '8px',
                  border: '1px solid #e5e7eb',
                  marginBottom: '16px'
                }}>
                  <div style={{ display: 'grid', gridTemplateColumns: '1fr 2fr auto', gap: '12px', alignItems: 'end' }}>
                    <div>
                      <label style={{ fontSize: '12px', color: '#6b7280', marginBottom: '4px', display: 'block' }}>
                        Sub-mood Name
                      </label>
                      <input
                        type="text"
                        value={newSubMood.name}
                        onChange={(e) => setNewSubMood({...newSubMood, name: e.target.value})}
                        onFocus={() => setSubMoodNameFocused(true)}
                        onBlur={() => setSubMoodNameFocused(false)}
                        style={{
                          width: '100%',
                          padding: '8px 12px',
                          border: `1px solid ${subMoodNameFocused ? '#006D77' : '#d1d5db'}`,
                          borderRadius: '6px',
                          fontSize: '14px',
                          outline: 'none',
                          transition: 'all 0.2s ease',
                          boxSizing: 'border-box'
                        }}
                        placeholder="e.g., Joyful"
                      />
                    </div>
                    <div>
                      <label style={{ fontSize: '12px', color: '#6b7280', marginBottom: '4px', display: 'block' }}>
                        Meaning/Description
                      </label>
                      <input
                        type="text"
                        value={newSubMood.meaning}
                        onChange={(e) => setNewSubMood({...newSubMood, meaning: e.target.value})}
                        onFocus={() => setSubMoodMeaningFocused(true)}
                        onBlur={() => setSubMoodMeaningFocused(false)}
                        style={{
                          width: '100%',
                          padding: '8px 12px',
                          border: `1px solid ${subMoodMeaningFocused ? '#006D77' : '#d1d5db'}`,
                          borderRadius: '6px',
                          fontSize: '14px',
                          outline: 'none',
                          transition: 'all 0.2s ease',
                          boxSizing: 'border-box'
                        }}
                        placeholder="Description of this sub-mood"
                      />
                    </div>
                    <div style={{ display: 'flex', gap: '8px' }}>
                      {editingSubMoodIndex >= 0 ? (
                        <>
                          <button
                            type="button"
                            onClick={updateSubMood}
                            style={{
                              padding: '8px 12px',
                              backgroundColor: '#006D77',
                              color: 'white',
                              border: 'none',
                              borderRadius: '6px',
                              fontSize: '12px',
                              cursor: 'pointer',
                              whiteSpace: 'nowrap'
                            }}
                          >
                            Update
                          </button>
                          <button
                            type="button"
                            onClick={cancelSubMoodEdit}
                            style={{
                              padding: '8px 12px',
                              backgroundColor: '#6b7280',
                              color: 'white',
                              border: 'none',
                              borderRadius: '6px',
                              fontSize: '12px',
                              cursor: 'pointer',
                              whiteSpace: 'nowrap'
                            }}
                          >
                            Cancel
                          </button>
                        </>
                      ) : (
                        <button
                          type="button"
                          onClick={addSubMood}
                          style={{
                            padding: '8px 12px',
                            backgroundColor: '#FF6F61',
                            color: 'white',
                            border: 'none',
                            borderRadius: '6px',
                            fontSize: '12px',
                            cursor: 'pointer',
                            whiteSpace: 'nowrap',
                            transition: 'background-color 0.2s ease'
                          }}
                          onMouseEnter={(e) => e.target.style.backgroundColor = 'rgba(255, 111, 97, 0.9)'}
                          onMouseLeave={(e) => e.target.style.backgroundColor = '#FF6F61'}
                        >
                          Add
                        </button>
                      )}
                    </div>
                  </div>
                </div>

                {/* Sub-moods List */}
                {formData.sub_moods.length > 0 && (
                  <div style={{
                    maxHeight: '200px',
                    overflowY: 'auto',
                    border: '1px solid #e5e7eb',
                    borderRadius: '8px'
                  }}>
                    {formData.sub_moods.map((subMood, index) => (
                      <div key={index} style={{
                        padding: '12px 16px',
                        borderBottom: index < formData.sub_moods.length - 1 ? '1px solid #f3f4f6' : 'none',
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        backgroundColor: editingSubMoodIndex === index ? '#f0fdfa' : 'white'
                      }}>
                        <div style={{ flex: 1 }}>
                          <div style={{ fontWeight: '500', color: '#374151', marginBottom: '4px' }}>
                            {subMood.name}
                          </div>
                          <div style={{ fontSize: '12px', color: '#6b7280' }}>
                            {subMood.meaning}
                          </div>
                        </div>
                        <div style={{ display: 'flex', gap: '8px' }}>
                          <button
                            type="button"
                            onClick={() => editSubMood(index)}
                            style={{
                              padding: '4px 8px',
                              backgroundColor: '#006D77',
                              color: 'white',
                              border: 'none',
                              borderRadius: '4px',
                              fontSize: '12px',
                              cursor: 'pointer'
                            }}
                          >
                            Edit
                          </button>
                          <button
                            type="button"
                            onClick={() => deleteSubMood(index)}
                            style={{
                              padding: '4px 8px',
                              backgroundColor: '#dc2626',
                              color: 'white',
                              border: 'none',
                              borderRadius: '4px',
                              fontSize: '12px',
                              cursor: 'pointer'
                            }}
                          >
                            Delete
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* Form Message */}
              {formMessage && (
                <div style={{
                  padding: '12px 16px',
                  backgroundColor: formMessage.includes('success') ? '#d1fae5' : '#fee2e2',
                  color: formMessage.includes('success') ? '#065f46' : '#991b1b',
                  borderRadius: '8px',
                  marginBottom: '20px',
                  fontSize: '14px',
                  textAlign: 'center'
                }}>
                  {formMessage}
                </div>
              )}

              {/* Form Buttons */}
              <div style={{
                display: 'flex',
                gap: '12px',
                justifyContent: 'flex-end'
              }}>
                <button
                  type="button"
                  onClick={handleFormCancel}
                  onMouseEnter={() => setCancelHovered(true)}
                  onMouseLeave={() => setCancelHovered(false)}
                  style={{
                    padding: '12px 24px',
                    backgroundColor: cancelHovered ? '#f3f4f6' : 'white',
                    color: '#374151',
                    border: '2px solid #e5e7eb',
                    borderRadius: '8px',
                    fontSize: '14px',
                    fontWeight: '500',
                    cursor: 'pointer',
                    transition: 'all 0.2s ease'
                  }}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={formLoading}
                  onMouseEnter={() => setSubmitHovered(true)}
                  onMouseLeave={() => setSubmitHovered(false)}
                  style={{
                    padding: '12px 24px',
                    backgroundColor: formLoading ? '#9ca3af' : (submitHovered ? 'rgba(255, 111, 97, 0.9)' : '#FF6F61'),
                    color: 'white',
                    border: 'none',
                    borderRadius: '8px',
                    fontSize: '14px',
                    fontWeight: '500',
                    cursor: formLoading ? 'not-allowed' : 'pointer',
                    transition: 'all 0.2s ease',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px'
                  }}
                >
                  {formLoading && (
                    <div style={{
                      width: '16px',
                      height: '16px',
                      border: '2px solid transparent',
                      borderTop: '2px solid white',
                      borderRadius: '50%',
                      animation: 'spin 1s linear infinite'
                    }}></div>
                  )}
                  {formLoading ? 'Processing...' : (formMode === 'add' ? 'Add Mood' : 'Update Mood')}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Custom Tooltip */}
      {tooltipVisible && (
        <div style={{
          position: 'fixed',
          left: tooltipPosition.x,
          top: tooltipPosition.y,
          transform: 'translateX(-50%) translateY(-100%)',
          backgroundColor: '#1f2937',
          color: 'white',
          padding: '8px 12px',
          borderRadius: '6px',
          fontSize: '13px',
          lineHeight: '1.3',
          maxWidth: '200px',
          zIndex: 1000,
          boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.25), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
          border: '1px solid rgba(255, 255, 255, 0.1)',
          pointerEvents: 'none'
        }}>
          {/* Tooltip Arrow */}
          <div style={{
            position: 'absolute',
            bottom: '-6px',
            left: '50%',
            transform: 'translateX(-50%)',
            width: 0,
            height: 0,
            borderLeft: '6px solid transparent',
            borderRight: '6px solid transparent',
            borderTop: '6px solid #1f2937'
          }}></div>

          {/* Tooltip Content */}
          {(() => {
            const currentMood = moods.find(m => m.id === tooltipVisible);
            if (!currentMood || !currentMood.sub_moods) return null;

            return currentMood.sub_moods.slice(8).map((subMood, index) => (
              <div key={index} style={{
                marginBottom: index < currentMood.sub_moods.slice(8).length - 1 ? '4px' : '0',
                color: '#e5e7eb',
                fontWeight: '500'
              }}>
                {subMood.name || 'Unknown Sub-mood'}
              </div>
            ));
          })()}
        </div>
      )}
    </div>
  );
}

export default Moods;
