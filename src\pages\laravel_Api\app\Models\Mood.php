<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Mood extends Model
{
    public function up()
    {
        Schema::create('moods', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->timestamps();
        });
    }
    protected $fillable = ['name'];

    public function subMoods()
    {
        return $this->hasMany(SubMood::class);
    }
    public function foodItems()
    {
        return $this->hasMany(FoodItem::class);
    }

}
